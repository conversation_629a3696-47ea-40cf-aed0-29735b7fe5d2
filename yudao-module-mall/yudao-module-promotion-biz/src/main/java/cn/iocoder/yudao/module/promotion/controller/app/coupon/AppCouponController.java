package cn.iocoder.yudao.module.promotion.controller.app.coupon;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.idempotent.core.annotation.Idempotent;
import cn.iocoder.yudao.framework.idempotent.core.keyresolver.impl.UserIdempotentKeyResolver;
import cn.iocoder.yudao.framework.security.core.annotations.PreAuthenticated;
import cn.iocoder.yudao.module.promotion.controller.app.coupon.vo.coupon.*;
import cn.iocoder.yudao.module.promotion.convert.coupon.CouponConvert;
import cn.iocoder.yudao.module.promotion.dal.dataobject.coupon.CouponDO;
import cn.iocoder.yudao.module.promotion.dal.dataobject.coupon.CouponTemplateDO;
import cn.iocoder.yudao.module.promotion.enums.coupon.CouponTakeTypeEnum;
import cn.iocoder.yudao.module.promotion.service.coupon.CouponService;
import cn.iocoder.yudao.module.promotion.service.coupon.CouponTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.error;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "用户 App - 优惠劵")
@RestController
@RequestMapping("/promotion/coupon")
@Validated
public class AppCouponController {

    @Resource
    private CouponService couponService;
    @Resource
    private CouponTemplateService couponTemplateService;

    @Resource
    private RedissonClient redissonClient;
    
    @PostMapping("/take")
    @Operation(summary = "领取优惠劵")
    @Parameter(name = "templateId", description = "优惠券模板编号", required = true, example = "1024")
    @Idempotent(message = "请求过于频繁，请稍后再试",keyResolver = UserIdempotentKeyResolver.class)
    @PreAuthenticated
    public CommonResult<Boolean> takeCoupon(@Valid @RequestBody AppCouponTakeReqVO reqVO) {
        boolean canTakeAgain = true;
        RLock lock = redissonClient.getLock("member_take_coupon:" + reqVO.getTemplateId());
        if(lock.tryLock()) {
            try {
                // 1. 领取优惠劵
                Long userId = getLoginUserId();
                couponService.takeCoupon(reqVO.getTemplateId(), CollUtil.newHashSet(userId), CouponTakeTypeEnum.USER);
                // 2. 检查是否可以继续领取
                CouponTemplateDO couponTemplate = couponTemplateService.getCouponTemplate(reqVO.getTemplateId());
                if (couponTemplate.getTakeLimitCount() != null && couponTemplate.getTakeLimitCount() > 0) {
                    Integer takeCount = couponService.getTakeCount(reqVO.getTemplateId(), userId);
                    canTakeAgain = takeCount < couponTemplate.getTakeLimitCount();
                }
            }finally {
                lock.unlock();
            }
        }else{
            return error(GlobalErrorCodeConstants.LOCKED);
        }
        return success(canTakeAgain);
    }

    @GetMapping("/match-list")
    @Operation(summary = "获得匹配指定商品的优惠劵列表", description = "用于下单页，展示优惠劵列表")
    public CommonResult<List<AppCouponMatchRespVO>> getMatchCouponList(AppCouponMatchReqVO matchReqVO) {
        // todo: 优化：优惠金额倒序
        List<CouponDO> list = couponService.getMatchCouponList(getLoginUserId(), matchReqVO);
        return success(BeanUtils.toBean(list, AppCouponMatchRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "我的优惠劵列表")
    @PreAuthenticated
    public CommonResult<PageResult<AppCouponRespVO>> getCouponPage(AppCouponPageReqVO pageReqVO) {
        LocalDateTime[] createTime = new LocalDateTime[2];
        LocalDateTime nowTime = LocalDateTime.now();
        createTime[0] = nowTime.minusMonths(pageReqVO.getRecentMonth()).withHour(0).withMinute(0).withSecond(0);
        createTime[1] = nowTime.withHour(23).withMinute(59).withSecond(59);
        pageReqVO.setCreateTime(createTime);
        PageResult<CouponDO> pageResult = couponService.getCouponPage(
                CouponConvert.INSTANCE.convert(pageReqVO, Collections.singleton(getLoginUserId())));
        return success(BeanUtils.toBean(pageResult, AppCouponRespVO.class));
    }

    @GetMapping("/get")
    @Operation(summary = "获得优惠劵")
    @Parameter(name = "id", description = "优惠劵编号", required = true, example = "1024")
    @PreAuthenticated
    public CommonResult<AppCouponRespVO> getCoupon(@RequestParam("id") Long id) {
        CouponDO coupon = couponService.getCoupon(getLoginUserId(), id);
        return success(BeanUtils.toBean(coupon, AppCouponRespVO.class));
    }

    @GetMapping(value = "/get-unused-count")
    @Operation(summary = "获得未使用的优惠劵数量")
    @PreAuthenticated
    public CommonResult<Long> getUnusedCouponCount() {
        return success(couponService.getUnusedCouponCount(getLoginUserId()));
    }

    @PostMapping("/business-use")
    @Operation(summary = "商家核销优惠劵")
    @PreAuthenticated
    public CommonResult<Boolean> businessUseCoupon(@Valid @RequestBody AppCouponUseReqVO reqVO) {
        Long userId = getLoginUserId();
        couponService.businessUseCoupon(reqVO,userId);
        return success(Boolean.TRUE);
    }

    @GetMapping("/business-page")
    @Operation(summary = "查询商家核销优惠劵列表")
    @PreAuthenticated
    public CommonResult<PageResult<AppCouponRespVO>> getBusinessCouponPage(AppCouponPageReqVO pageReqVO) {
        PageResult<CouponDO> pageResult = couponService.getCouponPage(
                CouponConvert.INSTANCE.convert(Collections.singleton(getLoginUserId()),pageReqVO));
        return success(BeanUtils.toBean(pageResult, AppCouponRespVO.class));
    }
}
