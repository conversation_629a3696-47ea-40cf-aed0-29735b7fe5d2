package cn.iocoder.yudao.module.oa.service.linefee;

import java.util.*;
import javax.validation.*;
import cn.iocoder.yudao.module.oa.controller.admin.linefee.vo.*;
import cn.iocoder.yudao.module.oa.dal.dataobject.linefee.LineFeeDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 线路费用配置 Service 接口
 *
 * <AUTHOR>
 */
public interface LineFeeService {

    /**
     * 创建线路费用配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createLineFee(@Valid LineFeeSaveReqVO createReqVO);

    /**
     * 更新线路费用配置
     *
     * @param updateReqVO 更新信息
     */
    void updateLineFee(@Valid LineFeeSaveReqVO updateReqVO);

    /**
     * 删除线路费用配置
     *
     * @param id 编号
     */
    void deleteLineFee(Long id);

    /**
     * 获得线路费用配置
     *
     * @param id 编号
     * @return 线路费用配置
     */
    LineFeeDO getLineFee(Long id);

    /**
     * 获得线路费用配置分页
     *
     * @param pageReqVO 分页查询
     * @return 线路费用配置分页
     */
    PageResult<LineFeeDO> getLineFeePage(LineFeePageReqVO pageReqVO);

    Boolean importLineFeeList(List<LineFeeRespVO> list);
}