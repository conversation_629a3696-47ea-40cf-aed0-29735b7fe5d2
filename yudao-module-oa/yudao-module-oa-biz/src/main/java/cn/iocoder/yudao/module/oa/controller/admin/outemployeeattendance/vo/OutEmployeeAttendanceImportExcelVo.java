package cn.iocoder.yudao.module.oa.controller.admin.outemployeeattendance.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false) // 设置 chain = false，避免导入有问题
public class OutEmployeeAttendanceImportExcelVo {
    /**
     * 学校
     */
    @ExcelProperty("学校")
    private String school;
    /**
     * 姓名
     */
    @ExcelProperty("姓名")
    private String name;
    /**
     * 联系电话
     */
    @ExcelProperty("联系电话")
    private String phoneNumber;
    /**
     * 所属月份
     */
    @DateTimeFormat("yyyy-MM")
    @ExcelProperty("所属月份")
    private String occurrenceTime;
    /**
     * 出勤天数
     */
    @ExcelProperty("出勤天数")
    private BigDecimal attendanceDay;
    /**
     * 上班天数
     */
    @ExcelProperty("上班天数")
    private BigDecimal workDay;
    /**
     * 法定节假日
     */
    @ExcelProperty("法定节假日")
    private BigDecimal legalHoliday;
    /**
     * 其他
     */
    @ExcelProperty("其他")
    private BigDecimal otherDay;
    /**
     * 缺勤天数
     */
    @ExcelProperty("缺勤天数")
    private BigDecimal absenceDay;
    /**
     * 假期天数
     */
    @ExcelProperty("假期天数")
    private BigDecimal holidayDay;
    /**
     * 停班天数
     */
    @ExcelProperty("停班天数")
    private BigDecimal stopWorkDat;
    /**
     * 员工动态
     */
    @ExcelProperty("员工动态")
    private String employeeDynamics;
}
