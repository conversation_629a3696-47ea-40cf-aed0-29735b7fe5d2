package cn.iocoder.yudao.module.oa.service.charteredmonthlysalary;

import java.util.*;
import javax.validation.*;
import cn.iocoder.yudao.module.oa.controller.admin.charteredmonthlysalary.vo.*;
import cn.iocoder.yudao.module.oa.dal.dataobject.charteredmonthlysalary.CharteredMonthlySalaryDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 包车月薪管理 Service 接口
 *
 * <AUTHOR>
 */
public interface CharteredMonthlySalaryService {

    /**
     * 创建包车月薪管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createCharteredMonthlySalary(@Valid CharteredMonthlySalarySaveReqVO createReqVO);

    /**
     * 更新包车月薪管理
     *
     * @param updateReqVO 更新信息
     */
    void updateCharteredMonthlySalary(@Valid CharteredMonthlySalarySaveReqVO updateReqVO);

    /**
     * 删除包车月薪管理
     *
     * @param id 编号
     */
    void deleteCharteredMonthlySalary(Long id);

    /**
     * 获得包车月薪管理
     *
     * @param id 编号
     * @return 包车月薪管理
     */
    CharteredMonthlySalaryDO getCharteredMonthlySalary(Long id);

    /**
     * 获得包车月薪管理分页
     *
     * @param pageReqVO 分页查询
     * @return 包车月薪管理分页
     */
    PageResult<CharteredMonthlySalaryDO> getCharteredMonthlySalaryPage(CharteredMonthlySalaryPageReqVO pageReqVO);

    /**
     * 确认包车月薪
     */
    public void confirmCharteredMonthlySalary(CharteredMonthlySalaryRespVO confirmReqVO);

    /**
     * 包车月薪生成
     *
     * @param generateVo
     * @return
     */
    CharteredMonthlySalaryRespVO generateCharteredMonthlySalary(CharteredMonthlySalaryRespVO generateVo);
}