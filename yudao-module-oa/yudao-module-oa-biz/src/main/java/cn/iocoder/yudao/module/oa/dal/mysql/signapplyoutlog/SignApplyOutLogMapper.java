package cn.iocoder.yudao.module.oa.dal.mysql.signapplyoutlog;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.oa.dal.dataobject.signapplyoutlog.SignApplyOutLogDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.oa.controller.admin.signapplyoutlog.vo.*;

/**
 * 标牌申请外部日志 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SignApplyOutLogMapper extends BaseMapperX<SignApplyOutLogDO> {

    default PageResult<SignApplyOutLogDO> selectPage(SignApplyOutLogPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SignApplyOutLogDO>()
                .eqIfPresent(SignApplyOutLogDO::getSignId, reqVO.getSignId())
                .eqIfPresent(SignApplyOutLogDO::getCurrentLocation, reqVO.getCurrentLocation())
                .eqIfPresent(SignApplyOutLogDO::getProcessStatus, reqVO.getProcessStatus())
                .likeIfPresent(SignApplyOutLogDO::getCreatorName, reqVO.getCreatorName())
                .betweenIfPresent(SignApplyOutLogDO::getFinishTime, reqVO.getFinishTime())
                .betweenIfPresent(SignApplyOutLogDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(SignApplyOutLogDO::getId));
    }

}