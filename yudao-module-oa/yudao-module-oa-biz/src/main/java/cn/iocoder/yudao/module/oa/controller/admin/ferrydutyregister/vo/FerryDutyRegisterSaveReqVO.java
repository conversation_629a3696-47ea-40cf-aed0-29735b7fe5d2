package cn.iocoder.yudao.module.oa.controller.admin.ferrydutyregister.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 摆渡值班登记新增/修改 Request VO")
@Data
public class FerryDutyRegisterSaveReqVO {

    @Schema(description = "主表 ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27458")
    private Long id;

    @Schema(description = "申请人 ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "30381")
    @NotNull(message = "申请人 ID不能为空")
    private Long applyId;

    @Schema(description = "同行人 ID", example = "11400")
    private Long partnerId;

    @Schema(description = "开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "开始时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String beginTime;

    @Schema(description = "结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "结束时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String endTime;

    @Schema(description = "外出时长(小时)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "外出时长(小时)不能为空")
    private Integer outingDuration;

    @Schema(description = "外出理由", requiredMode = Schema.RequiredMode.REQUIRED, example = "不好")
    @NotEmpty(message = "外出理由不能为空")
    private String outingReason;

    @Schema(description = "站点费用")
    private BigDecimal siteFee;

    @Schema(description = "站点名称", example = "芋艿")
    private String siteName;

}