package cn.iocoder.yudao.module.oa.dal.dataobject.driversalary;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 驾驶员员工薪酬 DO
 *
 * <AUTHOR>
 */
@TableName("t_driver_salary")
@KeySequence("t_driver_salary_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DriverSalaryDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 员工 ID
     */
    private Long personId;
    /**
     * 应发月薪
     */
    private BigDecimal monthlyGrossSalary;
    /**
     * 最终假期基数
     */
    private BigDecimal finalHolidayBase;
    /**
     * 发薪月数
     */
    private String payMonths;
    /**
     * 薪酬方式
     */
    private String salaryMethod;
    /**
     * 年薪
     */
    private BigDecimal annualSalary;
    /**
     * 原假期基数
     */
    private BigDecimal originalHolidayBase;
    /**
     * 薪酬说明
     */
    private String salaryDescription;
    /**
     * 绩效
     */
    private BigDecimal performance;

    /**
     * 学期绩效
     */
    private BigDecimal semesterPerformance;

    /**
     * 工龄奖(应发含)
     */
    private BigDecimal seniorityBonus;
    /**
     * 岗位工资
     */
    private BigDecimal positionSalary;
    /**
     * 基本工资
     */
    private BigDecimal baseSalary;
    /**
     * 全勤奖
     */
    private BigDecimal fullAttendanceBonus;
    /**
     * 调薪幅度(%)
     */
    private BigDecimal salaryAdjustmentRange;
    /**
     * 住房补贴
     */
    private BigDecimal housingAllowance;
    /**
     * 餐费补贴
     */
    private BigDecimal mealAllowance;
    /**
     * 线路补贴
     */
    private BigDecimal transportationAllowance;
    /**
     * 周接补贴
     */
    private BigDecimal weeklyTransferTimes;
    /**
     * 其他补贴
     */
    private BigDecimal otherAllowances;
    /**
     * 备注
     */
    private String remark;

}