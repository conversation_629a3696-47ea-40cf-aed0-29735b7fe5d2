package cn.iocoder.yudao.module.oa.dal.dataobject.assignedlocation;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 委派地列 DO
 *
 * <AUTHOR>
 */
@TableName("t_assigned_location")
@KeySequence("t_assigned_location_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AssignedLocationDO extends BaseDO {

    /**
     * ID
     */
    @TableId
    private Integer id;
    /**
     * 委派地
     */
    private String assignedLocation;
    /**
     * 类型（1：月薪人员；2：周薪人员）
     */
    private String type;

}