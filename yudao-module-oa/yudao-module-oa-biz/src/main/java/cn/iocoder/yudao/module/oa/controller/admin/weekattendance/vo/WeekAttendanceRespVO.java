package cn.iocoder.yudao.module.oa.controller.admin.weekattendance.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import java.math.BigDecimal;

import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 周薪考勤管理 Response VO")
@Data
@ExcelIgnoreUnannotated
@Accessors(chain = false)
public class WeekAttendanceRespVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "7553")
    private Long id;

    @Schema(description = "类别（职务id）", example = "7608")
    private Long postId;

    @Schema(description = "类别", example = "12830")
    @ExcelProperty("类别")
    private String postName;

    @Schema(description = "姓名", example = "张三")
    @ExcelProperty("姓名")
    private String name;

    @Schema(description = "联系电话")
    @ExcelProperty("联系电话")
    private String mobile;

    @Schema(description = "周起始日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("周起始日期")
    private String weekStartDate;

    @Schema(description = "周结束日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("周结束日期")
    private String weekEndDate;

    @Schema(description = "周数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("周数")
    private String week;

    @Schema(description = "周一费用")
    @ExcelProperty("周一费用")
    private BigDecimal mondayFee;

    @Schema(description = "周二费用")
    @ExcelProperty("周二费用")
    private BigDecimal tuesdayFee;

    @Schema(description = "周三费用")
    @ExcelProperty("周三费用")
    private BigDecimal wednesdayFee;

    @Schema(description = "周四费用")
    @ExcelProperty("周四费用")
    private BigDecimal thursdayFee;

    @Schema(description = "周五费用")
    @ExcelProperty("周五费用")
    private BigDecimal fridayFee;

    @Schema(description = "周六费用")
    @ExcelProperty("周六费用")
    private BigDecimal saturdayFee;

    @Schema(description = "周日费用")
    @ExcelProperty("周日费用")
    private BigDecimal sundayFee;

    @Schema(description = "补")
    @ExcelProperty("补")
    private BigDecimal backPay;

    @Schema(description = "合计")
    @ExcelProperty("合计")
    private BigDecimal totalFee;

    @Schema(description = "服务学校")
    @ExcelProperty("服务学校")
    private String school;

    @Schema(description = "状态", example = "0")
    private String status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;
}