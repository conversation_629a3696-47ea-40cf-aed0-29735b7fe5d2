package cn.iocoder.yudao.module.oa.controller.admin.assignedlocation.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 委派地列新增/修改 Request VO")
@Data
public class AssignedLocationSaveReqVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "32505")
    private Integer id;

    @Schema(description = "委派地")
    private String assignedLocation;

    @Schema(description = "类型（1：月薪人员；2：周薪人员）", example = "2")
    private String type;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新人")
    private String updateBy;

}