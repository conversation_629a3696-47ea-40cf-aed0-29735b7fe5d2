package cn.iocoder.yudao.module.oa.controller.admin.signapply.vo;

import cn.iocoder.yudao.module.oa.controller.admin.signapplynum.vo.SignApplyNumPrintVO;
import cn.iocoder.yudao.module.oa.controller.admin.signapplystop.vo.SignApplyStopPrintVO;
import cn.iocoder.yudao.module.oa.controller.admin.signapplytime.vo.SignApplyTimePrintVO;
import cn.iocoder.yudao.module.oa.dal.dataobject.signapplyline.SignApplyLineDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.signapplynum.SignApplyNumDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.signapplystop.SignApplyStopDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.signapplytime.SignApplyTimeDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 标牌申请详打印VO
 *
 * <AUTHOR>
 */
@Data
public class SignApplyPrintVO {
    /**
     * 标牌申请id
     */
    private Long signId;
    /**
     * 驾驶员姓名
     */
    private String driver;
    /**
     * 性别
     */
    private String sex;
    /**
     * 年龄
     */
    private Integer age;
    /**
     * 校车驾驶资格取得时间
     */
    private String acquireDate;
    /**
     * 准驾车型
     */
    private String allowModel;
    /**
     * 驾驶证审验有效期
     */
    private String dirverValidDate;
    /**
     * 联系电话
     */
    private String mobile;
    /**
     * 身份证
     */
    private String identity;
    /**
     * 备用驾驶员
     */
    private String backupDriver;
    /**
     * 备用驾驶员联系电话
     */
    private String backupPhone;
    /**
     * 备用驾驶员性别
     */
    private String backupSex;
    /**
     * 备用驾驶员年龄
     */
    private Integer backupAge;
    /**
     * 备用校车驾驶资格取得时间
     */
    private String backupAcquireDate;
    /**
     * 备用准驾车型
     */
    private String backupAllowModel;
    /**
     * 备用驾驶证审验有效期
     */
    private String backupDirverValidDate;
    /**
     * 照管人员汇总姓名
     */
    private String caretakerName;
    /**
     * 照管人员姓名
     */
    private String caretaker;
    /**
     * 照管人员性别
     */
    private String caretakerSex;
    /**
     * 照管人员年龄
     */
    private Integer caretakeAge;
    /**
     * 照管人员联系电话
     */
    private String caretakeMobile;
    /**
     * 照管人员姓名2
     */
    private String caretaker2;
    /**
     * 照管人员性别2
     */
    private String caretakerSex2;
    /**
     * 照管人员年龄2
     */
    private Integer caretakeAge2;
    /**
     * 照管人员联系电话2
     */
    private String caretakeMobile2;
    /**
     * 车牌号码
     */
    private String plateNumber;
    /**
     * 车型
     */
    private String model;
    /**
     * 发动机号
     */
    private String engineNo;
    /**
     * 核载人数
     */
    private Integer loadNumber;
    /**
     * 核载成人人数
     */
    private Integer adultNumber;
    /**
     * 核载学生人数
     */
    private Integer studentNumber;
    /**
     * 安全技术检验有效期
     */
    private String safeTechnologyDate;
    /**
     * 校车使用性质
     */
    private String schoolBusUsage;
    /**
     * 注册登记时间
     */
    private String registerDate;
    /**
     * 校车所有人
     */
    private String schoolOwner;
    /**
     * 号牌种类
     */
    private String plateType;
    /**
     * 车架号
     */
    private String vin;
    /**
     * 车辆报废时间
     */
    private String busScrapDate;
    /**
     * 品牌型号
     */
    private String brand;
    /**
     * 校车类型
     */
    private String schoolBusType1;
    /**
     * 校车类型
     */
    private String schoolBusType2;
    /**
     * 校车类型
     */
    private String schoolBusType3;
    /**
     * 汽车外观标识
     */
    private String appearance;
    /**
     * 甲方名称
     */
    private String firstParty;
    /**
     * 学校性质
     */
    private String schoolType;
    /**
     * 学校办学类型
     */
    private String schoolRunType;
    /**
     * 校长姓名
     */
    private String principal;
    /**
     * 校长联系电话
     */
    private String firstPartyMobile;
    /**
     * 甲方地址
     */
    private String firstPartyAddress;
    /**
     * 乙方姓名
     */
    private String secondParty;
    /**
     * 乙方性质
     */
    private String secondPartyType;
    /**
     * 法人代表
     */
    private String legalPerson;
    /**
     * 联系电话
     */
    private String secondPartyMobile;
    /**
     * 乙方地址
     */
    private String secondPartyAddress;
    /**
     * 随车照管人员由方指派
     */
    private String caretakeAppoint;
    /**
     * 本责任书有效期
     */
    private String responsibilityDate;
    /**
     * 保险投保情况
     */
    private String insurance;
    /**
     * 万元
     */
    private String insuranceMoney;

    /**
     * 万元2
     */
    private String insuranceMoney2;
    /**
     * 保险投保情况合同期限
     */
    private String insuranceDate;
    /**
     * 万元4
     */
    private String insuranceMoney4;
    /**
     * 万元5
     */
    private String insuranceMoney5;
    /**
     * 万元6
     */
    private String insuranceMoney6;
    /**
     * 开行时间
     */
    private String departureDate;
    /**
     * 行驶线路
     */
    private String drivingRoute;
    /**
     * 停靠站点
     */
    private String dockingStation;
    /**
     * 业务类型
     */
    private String businessType;
    /**
     * 领证方式
     */
    private String certificateType;
    /**
     * 属于类校车
     */
    private String belongBus;
    /**
     * 主要接送
     */
    private String mainTransfer;
    /**
     * 申请单位名称（公章）
     */
    private String applicant;
    /**
     * 法人签名
     */
    private String legalSignature;
    /**
     * 经办人签名
     */
    private String dealSignature;
    /**
     * 委托
     */
    private String commission;
    /**
     * 电话
     */
    private String commissionPhone;
    /**
     * 委托书有效期限：自签署之日起日内有效
     */
    private String commissionDay;
    /**
     * 委托方（盖章）
     */
    private String clientSeal;
    /**
     * 委托方签署日期
     */
    private String clientDate;
    /**
     * 受托方
     */
    private String entrust;
    /**
     * 受托方签署日期
     */
    private String entrustDate;
    /**
     * 校车运行线路图片（已作废）
     */
    private String linePicture;
    /**
     * 停靠站点图片（已作废）
     */
    private String stopPicture;
    /**
     * 照管人员身份证
     */
    private String caretakeIdentity;

    /**
     * 委托人身份证
     */
    private String commissionIdentity;

    /**
     * 校车运行线路图片
     */
    private List<String> linePictureList;
    /**
     * 停靠站点图片
     */
    private List<String> stopPictureList;
    /**
     * 收件人
     */
    private String recipient;
    /**
     * 收件人手机号码
     */
    private String recipientMobile;
    /**
     * 委托号牌种类
     */
    private String commissionPlateType;
    /**
     * 丙方姓名
     */
    private String thirdParty;
    /**
     * 校车申请标题
     */
    private String busApplyTitle;
    /**
     * 校车申请模板详细描述1
     */
    private String busDetailedDescription1;
    /**
     * 校车申请模板详细描述2
     */
    private String busDetailedDescription2;
    /**
     * 校车申请模板我方服务学校数量
     */
    private Integer oneselfSchoolNumber;
    /**
     * 校车申请模板我方服务学校车辆数量
     */
    private Integer oneselfBusNumber;
    /**
     * 校车申请模板服务单位学校车辆数量
     */
    private Integer serviceUnitsBusNumber;

    @Schema(description = "校车运行趟次的基本情况")
    private List<SignApplyNumPrintVO> signApplyNumList = new ArrayList<>();

    @Schema(description = "校车运行线路情况")
    private List<SignApplyLineDO> signApplyLineList = new ArrayList<>();

    @Schema(description = "校车运行时间情况")
    private List<SignApplyTimePrintVO> signApplyTimeList = new ArrayList<>();

    @Schema(description = "停靠站点情况")
    private List<SignApplyStopPrintVO> signApplyStopList = new ArrayList<>();

    /**
     * 校车运行线路情况(途径道路数量)
     */
    private Integer passRoadNum;
    /**
     * 校车运行线路情况(途径道路数组)
     */
    private String passRoads;
    /**
     * 途径镇街
     */
    private String passTowns;
    /**
     * 运行方案运行线路情况
     */
    private Map<String,List<SignApplyLineDO>> signApplyLineMap = new HashMap<>();
    /**
     * 运行方案运行时间情况
     */
    private Map<String,List<SignApplyTimePrintVO>> signApplyTimeMap = new HashMap<>();
}