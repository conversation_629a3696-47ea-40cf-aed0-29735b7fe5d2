package cn.iocoder.yudao.module.oa.controller.admin.weeksalaryconfirm.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 周薪确认 Response VO")
@Data
@ExcelIgnoreUnannotated
public class WeekSalaryConfirmRespVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "8687")
    @ExcelProperty("ID")
    private Long id;

    @Schema(description = "周期", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("周期")
    private String week;

    @Schema(description = "周起始日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("周起始日期")
    private String weekStartDate;

    @Schema(description = "周结束日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("周结束日期")
    private String weekEndDate;

    @Schema(description = "标题")
    @ExcelProperty("标题")
    private String title;

    @Schema(description = "状态", example = "2")
    @ExcelProperty("状态")
    private String status;

    @Schema(description = "确认时间")
    @ExcelProperty("确认时间")
    private LocalDateTime confirmTime;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}