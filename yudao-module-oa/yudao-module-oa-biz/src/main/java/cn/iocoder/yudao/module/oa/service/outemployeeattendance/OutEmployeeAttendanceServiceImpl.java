package cn.iocoder.yudao.module.oa.service.outemployeeattendance;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.module.oa.dal.dataobject.monthlysalaryemployees.MonthlySalaryEmployeesDO;
import cn.iocoder.yudao.module.oa.dal.mysql.monthlysalaryemployees.MonthlySalaryEmployeesMapper;
import cn.iocoder.yudao.module.system.dal.dataobject.dept.PostDO;
import cn.iocoder.yudao.module.system.dal.mysql.dept.PostMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;

import cn.iocoder.yudao.module.oa.controller.admin.outemployeeattendance.vo.*;
import cn.iocoder.yudao.module.oa.dal.dataobject.outemployeeattendance.OutEmployeeAttendanceDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.oa.dal.mysql.outemployeeattendance.OutEmployeeAttendanceMapper;

import java.lang.reflect.Field;
import java.util.*;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.oa.enums.ErrorCodeConstants.*;

/**
 * 外勤员工考勤 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class OutEmployeeAttendanceServiceImpl implements OutEmployeeAttendanceService {

    @Resource
    private OutEmployeeAttendanceMapper outEmployeeAttendanceMapper;
    @Resource
    private MonthlySalaryEmployeesMapper monthlySalaryEmployeesMapper;

    @Override
    public Integer createOutEmployeeAttendance(OutEmployeeAttendanceSaveReqVO createReqVO) {
        // 插入
        OutEmployeeAttendanceDO outEmployeeAttendance = BeanUtils.toBean(createReqVO, OutEmployeeAttendanceDO.class);
        outEmployeeAttendanceMapper.insert(outEmployeeAttendance);
        // 返回
        return outEmployeeAttendance.getId();
    }

    @Override
    public void updateOutEmployeeAttendance(OutEmployeeAttendanceSaveReqVO updateReqVO) {
        // 校验存在
        validateOutEmployeeAttendanceExists(updateReqVO.getId());
        // 更新
        OutEmployeeAttendanceDO updateObj = BeanUtils.toBean(updateReqVO, OutEmployeeAttendanceDO.class);
        outEmployeeAttendanceMapper.updateById(updateObj);
    }

    @Override
    public void deleteOutEmployeeAttendance(Integer id) {
        // 校验存在
        validateOutEmployeeAttendanceExists(id);
        // 删除
        outEmployeeAttendanceMapper.deleteById(id);
    }

    private void validateOutEmployeeAttendanceExists(Integer id) {
        if (outEmployeeAttendanceMapper.selectById(id) == null) {
            throw exception(OUT_EMPLOYEE_ATTENDANCE_NOT_EXISTS);
        }
    }

    @Override
    public OutEmployeeAttendanceDO getOutEmployeeAttendance(Integer id) {
        OutEmployeeAttendanceDO outEmployeeAttendanceDO = outEmployeeAttendanceMapper.selectById(id);
        MonthlySalaryEmployeesDO monthlySalaryEmployeesDO = monthlySalaryEmployeesMapper.selectById(outEmployeeAttendanceDO.getPersonId());
        outEmployeeAttendanceDO.setMonthlySalaryEmployeesDO(monthlySalaryEmployeesDO);
        return outEmployeeAttendanceDO;
    }

    @Override
    public PageResult<OutEmployeeAttendanceDO> getOutEmployeeAttendancePage(OutEmployeeAttendancePageReqVO pageReqVO) {
        if (!Objects.isNull(pageReqVO.getCreateTime())) {
            pageReqVO.setCreateTime(pageReqVO.getCreateTime().length < 2 ? null : pageReqVO.getCreateTime());
        }
        IPage<OutEmployeeAttendanceDO> iPage = outEmployeeAttendanceMapper.selectOutEmployeeAttendance(new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize()), pageReqVO);
        return new PageResult<>(iPage.getRecords(), iPage.getTotal());
    }

    public Boolean importOutEmployeeAttendance(List<OutEmployeeAttendanceImportExcelVo> list) {
        List<OutEmployeeAttendanceDO> insertList = new ArrayList<>();
        list.forEach(vo -> {
            if (StringUtils.isBlank(vo.getSchool())) {
                throw new ServiceException(500, "学校字段存在空值，请检查");
            }
            if (StringUtils.isBlank(vo.getName())) {
                throw new ServiceException(500, "名称字段存在空值，请检查");
            }
            if (StringUtils.isBlank(vo.getPhoneNumber())) {
                throw new ServiceException(500, "联系电话字段存在空值，请检查");
            }
            if (StringUtils.isBlank(vo.getOccurrenceTime())) {
                throw new ServiceException(500, "月份字段存在空值，请检查");
            }
            OutEmployeeAttendanceDO db = outEmployeeAttendanceMapper.selectOne(OutEmployeeAttendanceDO::getPhoneNumber, vo.getPhoneNumber(),
                    OutEmployeeAttendanceDO::getOccurrenceTime, vo.getOccurrenceTime());
            if (null == db) {
                db = new OutEmployeeAttendanceDO();
                org.springframework.beans.BeanUtils.copyProperties(vo, db);
                MonthlySalaryEmployeesDO monthlySalaryEmployeesDO = monthlySalaryEmployeesMapper.selectByPhoneNumber(db.getPhoneNumber());
                if (Objects.isNull(monthlySalaryEmployeesDO)) {
                    throw new ServiceException(new ErrorCode(500, "无法查询该用户【" + db.getName() + "】，手机号【" + db.getPhoneNumber() + "】花名册数据，请检查"));
                }
                db.setPersonId(monthlySalaryEmployeesDO.getId());
                insertList.add(db);
            } else {
                OutEmployeeAttendanceDO importData = new OutEmployeeAttendanceDO();
                org.springframework.beans.BeanUtils.copyProperties(vo, importData);
                Map<String, Object[]> compare = compare(db, importData);
                compare.remove("id");
                compare.remove("personId");
                compare.remove("phoneNumber");
                compare.remove("name");
                if (!compare.isEmpty()) {
                    throw new ServiceException(new ErrorCode(500, "用户【" + vo.getName() + "】已存在【" + vo.getOccurrenceTime() + "】数据，导入数据不相同，请检查后如需调整，编辑该数据"));
                }
            }
        });
        if (insertList.isEmpty()) {
            return true;
        }
        return outEmployeeAttendanceMapper.insertBatch(insertList);
    }

    public static Map<String, Object[]> compare(OutEmployeeAttendanceDO db, OutEmployeeAttendanceDO importData) {
        Map<String, Object[]> differences = new HashMap<>();
        // 获取类的所有字段
        Field[] fields = OutEmployeeAttendanceDO.class.getDeclaredFields();
        for (Field field : fields) {
            try {
                // 允许访问私有字段
                field.setAccessible(true);

                // 获取两个对象对应字段的值
                Object value1 = field.get(db);
                Object value2 = field.get(importData);

                // 比较值是否不同
                if (value1 == null && value2 == null) {
                    continue; // 两个值都为 null，跳过
                }
                if (value1 == null || value2 == null || !value1.equals(value2)) {
                    differences.put(field.getName(), new Object[]{value1, value2});
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }
        return differences;
    }

    /**
     * 通过花名册ID和所属月份获取外勤员工考勤记录
     * @param personId
     * @param occurrenceTime
     * @return
     */
    @Override
    public OutEmployeeAttendanceDO getOutEmployeeAttendance(Long personId, String occurrenceTime) {
        return outEmployeeAttendanceMapper.selectOne(OutEmployeeAttendanceDO::getPersonId, personId, OutEmployeeAttendanceDO::getOccurrenceTime, occurrenceTime);
    }

}