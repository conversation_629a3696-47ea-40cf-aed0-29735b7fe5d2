package cn.iocoder.yudao.module.oa.service.vehiclerecords;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONObject;
import cn.iocoder.yudao.framework.common.exception.ErrorCode;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.QueryWrapperX;
import cn.iocoder.yudao.module.oa.dal.dataobject.businfo.BusInfoDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.fuelconsumptioncurve.FuelConsumptionCurveDO;
import cn.iocoder.yudao.module.oa.dal.mysql.businfo.BusInfoMapper;
import cn.iocoder.yudao.module.oa.util.DgTrafficApiUtil;
import cn.iocoder.yudao.module.oa.util.DgTrafficLoginUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import cn.iocoder.yudao.module.oa.controller.admin.vehiclerecords.vo.*;
import cn.iocoder.yudao.module.oa.dal.dataobject.vehiclerecords.VehicleRecordsDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.oa.dal.mysql.vehiclerecords.VehicleRecordsMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.oa.enums.ErrorCodeConstants.*;

/**
 * 油量汇总 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class VehicleRecordsServiceImpl implements VehicleRecordsService {

    @Resource
    private VehicleRecordsMapper vehicleRecordsMapper;
    @Resource
    private BusInfoMapper busInfoMapper;
    @Resource
    private DgTrafficLoginUtil dgTrafficLoginUtil;
    @Resource
    private DgTrafficApiUtil dgTrafficApiUtil;

    //设置默认超时时间为 15分钟
    private static final int DEFAULT_TIMEOUT =  15 * 60*1000;

    @Override
    public Long createVehicleRecords(VehicleRecordsSaveReqVO createReqVO) {
        // 插入
        VehicleRecordsDO vehicleRecords = BeanUtils.toBean(createReqVO, VehicleRecordsDO.class);
        vehicleRecordsMapper.insert(vehicleRecords);
        // 返回
        return vehicleRecords.getId();
    }

    @Override
    public void updateVehicleRecords(VehicleRecordsSaveReqVO updateReqVO) {
        // 校验存在
        validateVehicleRecordsExists(updateReqVO.getId());
        // 更新
        VehicleRecordsDO updateObj = BeanUtils.toBean(updateReqVO, VehicleRecordsDO.class);
        vehicleRecordsMapper.updateById(updateObj);
    }

    @Override
    public void deleteVehicleRecords(Long id) {
        // 校验存在
        validateVehicleRecordsExists(id);
        // 删除
        vehicleRecordsMapper.deleteById(id);
    }

    private void validateVehicleRecordsExists(Long id) {
        if (vehicleRecordsMapper.selectById(id) == null) {
            throw exception(VEHICLE_RECORDS_NOT_EXISTS);
        }
    }

    @Override
    public VehicleRecordsDO getVehicleRecords(Long id) {
        return vehicleRecordsMapper.selectById(id);
    }

    @Override
    public PageResult<VehicleRecordsDO> getVehicleRecordsPage(VehicleRecordsPageReqVO pageReqVO) {
        return vehicleRecordsMapper.selectPage(pageReqVO);
    }


    /**
     * 同步车辆记录
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param cardNo 卡号
     */
    @Override
    public void syncVehicleRecords(String startTime, String endTime, String cardNo) throws Exception{
        Map<String, String> carNoAndBusOwnerMap = new HashMap<>();
        List<String> vehiIds = new ArrayList<>();
        String startTimeStr ="";
        String endTimeStr="";
        //startTime 和 endTime为空则默认查前一天的记录
        if(startTime == null && endTime == null){
            DateTime yesterday = DateUtil.yesterday();
            startTimeStr = DateUtil.format(yesterday, "yyyy-MM-dd");
            endTimeStr = DateUtil.format(yesterday, "yyyy-MM-dd");
        }else{
            //startTime 和 endTime 要么都为空，要么都不能为空
            if(startTime != null && endTime != null){
                try {
                    DateTime parse1 = DateUtil.parse(startTime, "yyyy-MM-dd");
                    DateTime parse2 = DateUtil.parse(endTime, "yyyy-MM-dd");
                    if(parse1.isAfter(parse2)){
                        throw new ServiceException(new ErrorCode(2_100_004,"开始时间不能大于结束时间"));
                    }
                    startTimeStr = DateUtil.format(parse1, "yyyy-MM-dd");
                    endTimeStr = DateUtil.format(parse2, "yyyy-MM-dd");
                }catch (Exception e){
                    e.printStackTrace();
                    throw new ServiceException(new ErrorCode(2_100_004,"时间格式错误"));
                }
            }else {
                throw new ServiceException(new ErrorCode(2_100_004,"开始时间和结束时间必须同时为空或同时不为空"));
            }
        }
        //车牌号是否为空，为空则查所有车牌号
        if(StringUtils.isBlank(cardNo)){
            List<BusInfoDO>carNos = busInfoMapper.selectList();
            vehiIds = carNos.stream().map(BusInfoDO::getPlateNumber).collect(Collectors.toList());
            //将车牌号作为 Key,所有人作为 Value 存进 Map 中
            carNoAndBusOwnerMap  = carNos.stream().collect(Collectors.toMap(BusInfoDO::getPlateNumber, BusInfoDO::getBusOwner));
        }else {
            //校验车牌有效性
            BusInfoDO plateNumber = busInfoMapper.selectOne(new QueryWrapperX<BusInfoDO>().eq("plate_number", cardNo));
            vehiIds  = Collections.singletonList(cardNo);
            carNoAndBusOwnerMap = Collections.singletonMap(cardNo, plateNumber.getBusOwner());
            if (plateNumber == null){
                throw new ServiceException(new ErrorCode(2_100_004,"车牌号:"+cardNo+"无效"));
            }
        }
        List<VehicleRecordsDO> oilSummaryData =new ArrayList<>();
        // 获取油耗汇总数据
        if (CollectionUtil.isNotEmpty(vehiIds)) {
            List<VehicleRecordsDO> allOilSummaryData = new ArrayList<>();

            // 将车牌号列表分批，每批最多100个
            int batchSize = 100;
            int totalSize = vehiIds.size();

            for (int i = 0; i < totalSize; i += batchSize) {
                // 计算当前批次的结束索引
                int endIndex = Math.min(i + batchSize, totalSize);

                // 获取当前批次的车牌号列表
                List<String> batchVehiIds = vehiIds.subList(i, endIndex);

                log.info("正在获取第 {}/{} 批车辆油耗数据，本批次包含 {} 个车牌",
                        (i / batchSize) + 1,
                        (totalSize + batchSize - 1) / batchSize,
                        batchVehiIds.size());

                // 调用API获取当前批次的数据
                List<VehicleRecordsDO> batchData = dgTrafficApiUtil.getOilSummaryData(
                        startTimeStr, endTimeStr, batchVehiIds, null, null, DEFAULT_TIMEOUT);

                // 将当前批次的数据添加到总结果中
                if (CollectionUtil.isNotEmpty(batchData)) {
                    allOilSummaryData.addAll(batchData);
                    log.info("成功获取第 {}/{} 批车辆油耗数据，获取到 {} 条记录",
                            (i / batchSize) + 1,
                            (totalSize + batchSize - 1) / batchSize,
                            batchData.size());
                } else {
                    log.warn("第 {}/{} 批车辆油耗数据为空",
                            (i / batchSize) + 1,
                            (totalSize + batchSize - 1) / batchSize);
                }

                // 可选：添加一个短暂的延迟，避免频繁请求第三方接口
                try {
                    Thread.sleep(200); // 500毫秒延迟
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }

            // 使用合并后的所有批次数据
            oilSummaryData = allOilSummaryData;
        } else {
            oilSummaryData = new ArrayList<>();
        }
        //拿到车牌号，批量查询车辆信息表
        for (VehicleRecordsDO oilSummaryDatum : oilSummaryData) {
            oilSummaryDatum.setCompanyName(carNoAndBusOwnerMap.get(oilSummaryDatum.getVehildno()));
        }

        if(CollectionUtil.isNotEmpty(oilSummaryData)){
            //将oilTrackDetailData中的车牌号组成为一个 List
            List<String> pullVehiIds = oilSummaryData.stream().map(VehicleRecordsDO::getVehildno).collect(Collectors.toList());
            List<VehicleRecordsDO>needDeleteQueries = vehicleRecordsMapper
                    .selectList(new QueryWrapperX<VehicleRecordsDO>().ge("begin_time", startTimeStr+" 00:00:00")
                            .le("end_time", endTimeStr+" 23:59:59")
                            .in("vehildno", pullVehiIds));
            if(CollectionUtil.isNotEmpty(needDeleteQueries)){
                List<Long> deleteIds = needDeleteQueries.stream().map(VehicleRecordsDO::getId).collect(Collectors.toList());
                //批量删除
                vehicleRecordsMapper.deleteBatchIds(deleteIds);
            }
            //批量插入数据库
            vehicleRecordsMapper.insertBatch(oilSummaryData);
        }

        log.info("获取到{}条油耗汇总数据", oilSummaryData.size());
    }}