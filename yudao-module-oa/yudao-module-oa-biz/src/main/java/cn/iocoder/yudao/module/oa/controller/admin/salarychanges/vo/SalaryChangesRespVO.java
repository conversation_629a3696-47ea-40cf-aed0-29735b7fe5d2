package cn.iocoder.yudao.module.oa.controller.admin.salarychanges.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 薪酬变动 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SalaryChangesRespVO {

    @Schema(description = "主表 ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "29645")
    @ExcelProperty("主表 ID")
    private Long id;

    @Schema(description = "变更对象员工 ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "7493")
    @ExcelProperty("变更对象员工 ID")
    private Long personId;

    @Schema(description = "变更金额类目", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("变更金额类目")
    private String changeCategory;

    @Schema(description = "变更前信息", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("变更前信息")
    private String infoBeforeChange;

    @Schema(description = "变更后信息", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("变更后信息")
    private String infoAfterChange;

    @Schema(description = "变更日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("变更日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String changeTime;

    @Schema(description = "操作人ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27844")
    @ExcelProperty("操作人ID")
    private Long changeUserId;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("更新时间")
    private LocalDateTime updateTime;

    //关联查询字段

    @Schema(description = "员工姓名", example = "王五")
    @TableField(exist = false)
    private String name;

    @Schema(description = "所属公司", example = "无限想象力公司")
    @TableField(exist = false)
    private String companyName;

    @Schema(description = "部门", example = "业务部")
    @TableField(exist = false)
    private String deptName;

    @Schema(description = "职务", example = "职员")
    @TableField(exist = false)
    private String postName;

    @Schema(description = "个人证件号", example = "123456789012345678")
    @ExcelProperty("个人证件号")
    @TableField(exist = false)
    private String idCardNumber;

    @Schema(description = "操作人姓名", example = "王五")
    @ExcelProperty("操作人姓名")
    @TableField(exist = false)
    private String changeUserName;

}