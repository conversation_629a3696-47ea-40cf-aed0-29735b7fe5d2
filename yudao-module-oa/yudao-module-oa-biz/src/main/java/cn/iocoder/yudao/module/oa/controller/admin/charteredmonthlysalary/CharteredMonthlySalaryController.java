package cn.iocoder.yudao.module.oa.controller.admin.charteredmonthlysalary;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.oa.controller.admin.charteredmonthlysalary.vo.*;
import cn.iocoder.yudao.module.oa.dal.dataobject.charteredmonthlysalary.CharteredMonthlySalaryDO;
import cn.iocoder.yudao.module.oa.service.charteredmonthlysalary.CharteredMonthlySalaryService;

@Tag(name = "管理后台 - 包车月薪管理")
@RestController
@RequestMapping("/oa/chartered-monthly-salary")
@Validated
public class CharteredMonthlySalaryController {

    @Resource
    private CharteredMonthlySalaryService charteredMonthlySalaryService;

    @PostMapping("/create")
    @Operation(summary = "创建包车月薪管理")
    @PreAuthorize("@ss.hasPermission('oa:chartered-monthly-salary:create')")
    public CommonResult<Long> createCharteredMonthlySalary(@Valid @RequestBody CharteredMonthlySalarySaveReqVO createReqVO) {
        return success(charteredMonthlySalaryService.createCharteredMonthlySalary(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新包车月薪管理")
    @PreAuthorize("@ss.hasPermission('oa:chartered-monthly-salary:update')")
    public CommonResult<Boolean> updateCharteredMonthlySalary(@Valid @RequestBody CharteredMonthlySalarySaveReqVO updateReqVO) {
        charteredMonthlySalaryService.updateCharteredMonthlySalary(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除包车月薪管理")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('oa:chartered-monthly-salary:delete')")
    public CommonResult<Boolean> deleteCharteredMonthlySalary(@RequestParam("id") Long id) {
        charteredMonthlySalaryService.deleteCharteredMonthlySalary(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得包车月薪管理")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('oa:chartered-monthly-salary:query')")
    public CommonResult<CharteredMonthlySalaryRespVO> getCharteredMonthlySalary(@RequestParam("id") Long id) {
        CharteredMonthlySalaryDO charteredMonthlySalary = charteredMonthlySalaryService.getCharteredMonthlySalary(id);
        return success(BeanUtils.toBean(charteredMonthlySalary, CharteredMonthlySalaryRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得包车月薪管理分页")
    @PreAuthorize("@ss.hasPermission('oa:chartered-monthly-salary:query')")
    public CommonResult<PageResult<CharteredMonthlySalaryRespVO>> getCharteredMonthlySalaryPage(@Valid CharteredMonthlySalaryPageReqVO pageReqVO) {
        PageResult<CharteredMonthlySalaryDO> pageResult = charteredMonthlySalaryService.getCharteredMonthlySalaryPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CharteredMonthlySalaryRespVO.class));
    }

    @PostMapping("/createMonthlySalary")
    @Operation(summary = "包车记录月薪生成")
    @PreAuthorize("@ss.hasPermission('oa:chartered-monthly-salary:generate')")
    public CommonResult<CharteredMonthlySalaryRespVO> generateCharteredMonthlySalary(@Valid @RequestBody CharteredMonthlySalaryRespVO monthlySalaryReqVO) {
       return success(charteredMonthlySalaryService.generateCharteredMonthlySalary(monthlySalaryReqVO));
    }

    @PutMapping("/confirm")
    @Operation(summary = "包车月薪确认")
    @PreAuthorize("@ss.hasPermission('oa:chartered-monthly-salary:confirm')")
    public CommonResult<Boolean> confirmCharteredMonthlySalary(@Valid @RequestBody CharteredMonthlySalaryRespVO confirmReqVO) {
        charteredMonthlySalaryService.confirmCharteredMonthlySalary(confirmReqVO);
        return success(true);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出包车月薪管理 Excel")
    @PreAuthorize("@ss.hasPermission('oa:chartered-monthly-salary:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportCharteredMonthlySalaryExcel(@Valid CharteredMonthlySalaryPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<CharteredMonthlySalaryDO> list = charteredMonthlySalaryService.getCharteredMonthlySalaryPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "包车月薪管理.xls", "数据", CharteredMonthlySalaryRespVO.class,
                        BeanUtils.toBean(list, CharteredMonthlySalaryRespVO.class));
    }

}