package cn.iocoder.yudao.module.oa.controller.admin.ferryline;

import io.swagger.v3.oas.annotations.Parameters;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.oa.controller.admin.ferryline.vo.*;
import cn.iocoder.yudao.module.oa.dal.dataobject.ferryline.FerryLineDO;
import cn.iocoder.yudao.module.oa.service.ferryline.FerryLineService;
import org.springframework.web.multipart.MultipartFile;

@Tag(name = "管理后台 - 摆渡线路管理")
@RestController
@RequestMapping("/oa/ferry-line")
@Validated
public class FerryLineController {

    @Resource
    private FerryLineService ferryLineService;

    @PostMapping("/create")
    @Operation(summary = "创建摆渡线路管理")
    @PreAuthorize("@ss.hasPermission('oa:ferry-line:create')")
    public CommonResult<Long> createFerryLine(@Valid @RequestBody FerryLineSaveReqVO createReqVO) {
        return success(ferryLineService.createFerryLine(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新摆渡线路管理")
    @PreAuthorize("@ss.hasPermission('oa:ferry-line:update')")
    public CommonResult<Boolean> updateFerryLine(@Valid @RequestBody FerryLineSaveReqVO updateReqVO) {
        ferryLineService.updateFerryLine(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除摆渡线路管理")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('oa:ferry-line:delete')")
    public CommonResult<Boolean> deleteFerryLine(@RequestParam("id") Long id) {
        ferryLineService.deleteFerryLine(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得摆渡线路管理")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('oa:ferry-line:query')")
    public CommonResult<FerryLineRespVO> getFerryLine(@RequestParam("id") Long id) {
        FerryLineDO ferryLine = ferryLineService.getFerryLine(id);
        return success(BeanUtils.toBean(ferryLine, FerryLineRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得摆渡线路管理分页")
    @PreAuthorize("@ss.hasPermission('oa:ferry-line:query')")
    public CommonResult<PageResult<FerryLineRespVO>> getFerryLinePage(@Valid FerryLinePageReqVO pageReqVO) {
        PageResult<FerryLineDO> pageResult = ferryLineService.getFerryLinePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, FerryLineRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出摆渡线路管理 Excel")
    @PreAuthorize("@ss.hasPermission('oa:ferry-line:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportFerryLineExcel(@Valid FerryLinePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<FerryLineDO> list = ferryLineService.getFerryLinePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "摆渡线路管理.xls", "Sheet1", FerryLineRespVO.class,
                        BeanUtils.toBean(list, FerryLineRespVO.class));
    }

    @GetMapping("/get-import-template")
    @Operation(summary = "获得摆渡线路管理模板")
    public void importTemplate(HttpServletResponse response) throws IOException {
        // 输出
        ExcelUtils.write(response, "摆渡线路管理导入模板.xls", "Sheet1", FerryLineRespVO.class, new ArrayList<>());
    }

    @PostMapping("/import")
    @Operation(summary = "导入摆渡线路管理")
    @Parameters({
            @Parameter(name = "file", description = "Excel 文件", required = true),
    })
    @PreAuthorize("@ss.hasPermission('oa:ferry-line:import')")
    public CommonResult<Boolean> importExcel(@RequestParam("file") MultipartFile file) throws Exception {
        List<FerryLineRespVO> list = ExcelUtils.read(file, FerryLineRespVO.class);
        return success(ferryLineService.importFerryLineList(list));
    }

    @GetMapping("/getschool")
    @Operation(summary = "获取所有学校")
    @PreAuthorize("@ss.hasPermission('oa:ferry-line:query')")
    public CommonResult<List<String>> getAllSchool() {

        return success(ferryLineService.getAllSchool());
    }

    @GetMapping("/getlinebyschool")
    @Operation(summary = "根据学校获取线路")
    @PreAuthorize("@ss.hasPermission('oa:ferry-line:query')")
    public CommonResult<List<FerryLineDO>> getLineBySchool(@RequestParam(value = "school", required = false) String school) {
        List<FerryLineDO> lineBySchool = new ArrayList<>();
        if(school!=null && !school.isEmpty()){
            lineBySchool = ferryLineService.getLineBySchool(school);
        }else{
            lineBySchool=null;
        }
        return success(lineBySchool);
    }

    @GetMapping("/getferryline")
    @Operation(summary = "根据学校、线路名称获得摆渡线路管理")
    @PreAuthorize("@ss.hasPermission('oa:ferry-line:query')")
    public CommonResult<FerryLineRespVO> getFerryLineBySchoolandlineName(@RequestParam("school") String school,@RequestParam("lineName") String lineName) {
        return success(ferryLineService.getFerryLineBySchoolandlineName(school,lineName));
    }

    @GetMapping("/getTypeBySchool")
    @Operation(summary = "根据学校获取线路类别")
    @PreAuthorize("@ss.hasPermission('oa:ferry-line:query')")
    public CommonResult<List<FerryLineTypeRespVO>> getLineTypeBySchool(@RequestParam(value = "school", required = false) String school) {
        if(StringUtils.isEmpty(school)){
            return success(new ArrayList<>());
        }
        return success(ferryLineService.getLineTypeBySchool(school));
    }
}
