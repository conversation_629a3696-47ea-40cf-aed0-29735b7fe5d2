package cn.iocoder.yudao.module.oa.controller.admin.signapplyline.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 校车运行线路情况分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SignApplyLinePageReqVO extends PageParam {

    @Schema(description = "标牌申请id", example = "10777")
    private Long signId;

    @Schema(description = "运行趟次")
    private String runNum;

    @Schema(description = "途径镇街")
    private String passTowm;

    @Schema(description = "途径道路")
    private String passRoad;

    @Schema(description = "道路基本情况")
    private String basicInfo;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    private Long ident;
    private Long level;
    private Long parentId;
    private String isParent;
    private Long rowspan;
}