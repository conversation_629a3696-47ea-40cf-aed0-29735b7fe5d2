package cn.iocoder.yudao.module.oa.service.oacompany;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;
import cn.iocoder.yudao.framework.mybatis.core.query.QueryWrapperX;
import cn.iocoder.yudao.module.oa.commons.constants.OaConstant;
import cn.iocoder.yudao.module.oa.dal.dataobject.officestaffsalary.OfficeStaffSalaryDO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import cn.iocoder.yudao.module.oa.controller.admin.oacompany.vo.*;
import cn.iocoder.yudao.module.oa.dal.dataobject.oacompany.OaCompanyDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.oa.dal.mysql.oacompany.OaCompanyMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.oa.enums.ErrorCodeConstants.*;

/**
 * 公司信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class OaCompanyServiceImpl implements OaCompanyService {

    @Resource
    private OaCompanyMapper companyMapper;
    @Resource
    private OaCompanyMapper oaCompanyMapper;

    @Override
    public Long createCompany(OaCompanySaveReqVO createReqVO) {

        //唯一性校验
        validateProductUnitNameUnique(createReqVO);

        // 插入
        OaCompanyDO company = BeanUtils.toBean(createReqVO, OaCompanyDO.class);
        if(company.getCreator()==null) {
            company.setStatus(OaConstant.STATUS_NORMAL);//默认为正常
        }
        companyMapper.insert(company);
        // 返回
        return company.getId();
    }

    @Override
    public void updateCompany(OaCompanySaveReqVO updateReqVO) {
        // 校验存在
        validateCompanyExists(updateReqVO.getId());

        //唯一性校验
        validateProductUnitNameUnique(updateReqVO);

        // 更新
        OaCompanyDO updateObj = BeanUtils.toBean(updateReqVO, OaCompanyDO.class);
        companyMapper.updateById(updateObj);
    }

    @Override
    public void deleteCompany(Long id) {
        // 校验存在
        validateCompanyExists(id);
        // 删除
        companyMapper.deleteById(id);
    }

    private void validateCompanyExists(Long id) {
        if (companyMapper.selectById(id) == null) {
            throw exception(COMPANY_NOT_EXISTS);
        }
    }

    //唯一性校验
    void validateProductUnitNameUnique(OaCompanySaveReqVO reqVO) {
        Long id = reqVO.getId();
        validateFieldUnique("company_code", "公司编码", reqVO.getCompanyCode(), id);
        validateFieldUnique("license", "营业执照", reqVO.getLicense(), id);
    }

    private void validateFieldUnique(String fieldName, String fieldDesc,Object fieldValue, Long id) {
        if (fieldValue == null) {
            return; // 如果字段值为空，跳过校验
        }

        if(StringUtils.isBlank(fieldDesc)){
            fieldDesc=fieldName;
        }
        QueryWrapperX<OaCompanyDO> queryWrapper = new QueryWrapperX<>();
        queryWrapper.eq("deleted", false)
                .eq(fieldName, fieldValue);

        // 修改场景：排除当前记录本身
        if (id != null && id > 0) {
            queryWrapper.ne("id", id);
        }
        Long count = oaCompanyMapper.selectCount(queryWrapper);
        if (count > 0) {
            throw exception(new ErrorCode(2_100_004, "新增或修改公司信息失败，" +fieldDesc + " 已存在：" + fieldValue));
        }
    }

    @Override
    public OaCompanyDO getCompany(Long id) {
        return companyMapper.selectById(id);
    }

    @Override
    public PageResult<OaCompanyDO> getCompanyPage(OaCompanyPageReqVO pageReqVO) {
        return companyMapper.selectPage(pageReqVO);
    }

    /**
     * 公司信息下拉列表
     * @return
     */
    @Override
    public List<OaCompanyDO> getCompanyList() {
        return companyMapper.selectList(new QueryWrapperX<OaCompanyDO>()
                .eq("status", OaConstant.STATUS_NORMAL));
    }
}