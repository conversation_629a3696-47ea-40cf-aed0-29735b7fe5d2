package cn.iocoder.yudao.module.oa.controller.admin.busroutes.vo;

import cn.iocoder.yudao.module.oa.controller.admin.busstations.vo.BusStationsSaveRoutesVO;
import cn.iocoder.yudao.module.oa.controller.admin.pathways.vo.PathWaysSaveRoutesVO;
import cn.iocoder.yudao.module.oa.dal.dataobject.busstations.BusStationsDO;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Author：xiawenze
 * @Package：cn.iocoder.yudao.module.oa.controller.admin.busroutes.vo
 * @Project：OAsystem
 * @name：BusRoutesSaveRequestVO
 * @Date：2025/3/31 15:18
 */
@Schema(description = "管理后台 - 线路列新增/修改 Request VO")
@Data
public class BusRoutesSaveRequestVO {

    @Schema(description = "主键自增ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "26455")
    private Long id;

    @Schema(description = "线路编号", example = "26455")
    private String routeNumber;

    @Schema(description = "线路名称", example = "芋艿")
    private String routeName;

    @Schema(description = "起始站点")
    private BusStationsDO startStation;

    @Schema(description = "终点站点")
    private BusStationsDO endStation;

    @Schema(description = "途径道路")
    private List<PathWaysSaveRoutesVO> wayPoints;

    @Schema(description = "下车站点")
    private List<BusStationsSaveRoutesVO> wayDownpoints;

    @Schema(description = "线路详细信息")
    private String routeDetail;

    @Schema(description = "服务学校")
    private String school;
}
