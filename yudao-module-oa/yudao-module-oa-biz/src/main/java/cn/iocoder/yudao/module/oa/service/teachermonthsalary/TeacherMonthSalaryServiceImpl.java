package cn.iocoder.yudao.module.oa.service.teachermonthsalary;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.iocoder.yudao.framework.common.exception.ErrorCode;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.mybatis.core.query.QueryWrapperX;
import cn.iocoder.yudao.module.oa.commons.constants.OaConstant;
import cn.iocoder.yudao.module.oa.commons.enums.SalaryMethodEnum;
import cn.iocoder.yudao.module.oa.controller.admin.monthlysalaryemployees.vo.MonthlySalaryEmployeesPageReqVO;
import cn.iocoder.yudao.module.oa.dal.dataobject.caregiversalary.CaregiverSalaryDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.housingfund.HousingFundDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.monthlysalaryemployees.MonthlySalaryEmployeesDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.otherfee.OtherFeeDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.outemployeeattendance.OutEmployeeAttendanceDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.overtimesubsidy.OvertimeSubsidyDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.socialsecurity.SocialSecurityDO;
import cn.iocoder.yudao.module.oa.dal.mysql.caregiversalary.CaregiverSalaryMapper;
import cn.iocoder.yudao.module.oa.dal.mysql.housingfund.HousingFundMapper;
import cn.iocoder.yudao.module.oa.dal.mysql.monthlyattendance.MonthlyAttendanceMapper;
import cn.iocoder.yudao.module.oa.dal.mysql.monthlysalaryemployees.MonthlySalaryEmployeesMapper;
import cn.iocoder.yudao.module.oa.dal.mysql.otherfee.OtherFeeMapper;
import cn.iocoder.yudao.module.oa.dal.mysql.outemployeeattendance.OutEmployeeAttendanceMapper;
import cn.iocoder.yudao.module.oa.dal.mysql.overtimesubsidy.OvertimeSubsidyMapper;
import cn.iocoder.yudao.module.oa.dal.mysql.socialsecurity.SocialSecurityMapper;
import cn.iocoder.yudao.module.oa.util.CheckUtil;
import cn.iocoder.yudao.module.system.dal.dataobject.dept.PostDO;
import cn.iocoder.yudao.module.system.dal.mysql.dept.PostMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

import cn.iocoder.yudao.module.oa.controller.admin.teachermonthsalary.vo.*;
import cn.iocoder.yudao.module.oa.dal.dataobject.teachermonthsalary.TeacherMonthSalaryDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.oa.dal.mysql.teachermonthsalary.TeacherMonthSalaryMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.oa.enums.ErrorCodeConstants.*;

/**
 * 跟车老师工资 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TeacherMonthSalaryServiceImpl implements TeacherMonthSalaryService {
    protected final Logger logger = LoggerFactory.getLogger(getClass());
    //静态锁对象，用于控制并发
    private static final Object CALCULATION_LOCK = new Object();
    //记录当前是否有正在计算薪资的任务在执行
    private static volatile boolean calculationInProgress = false;
    //记录计算任务开始时间
    private static volatile long calculationStartTime = 0;

    @Resource
    private TeacherMonthSalaryMapper teacherMonthSalaryMapper;

    @Resource
    private MonthlySalaryEmployeesMapper monthlySalaryEmployeesMapper; //月薪花名册表
    @Resource
    private PostMapper postMapper; //职务表
    @Resource
    private MonthlyAttendanceMapper monthlyAttendanceMapper; //月薪考勤表
    @Resource
    private OtherFeeMapper otherFeeMapper; //其他费用表
    @Resource
    private OvertimeSubsidyMapper overtimeSubsidyMapper; //加班表
    @Resource
    private SocialSecurityMapper socialSecurityMapper; //社保表
    @Resource
    private CaregiverSalaryMapper caregiverSalaryMapper; // 照管员薪酬表
    @Resource
    private OutEmployeeAttendanceMapper outEmployeeAttendanceMapper;
    @Resource
    private HousingFundMapper housingFundMapper;

    @Override
    public Long createTeacherMonthSalary(TeacherMonthSalarySaveReqVO createReqVO) {
        // 插入
        TeacherMonthSalaryDO teacherMonthSalary = BeanUtils.toBean(createReqVO, TeacherMonthSalaryDO.class);
        teacherMonthSalaryMapper.insert(teacherMonthSalary);
        // 返回
        return teacherMonthSalary.getId();
    }

    @Override
    public void updateTeacherMonthSalary(TeacherMonthSalarySaveReqVO updateReqVO) {
        // 校验存在
        validateTeacherMonthSalaryExists(updateReqVO.getId());
        // 更新
        TeacherMonthSalaryDO updateObj = BeanUtils.toBean(updateReqVO, TeacherMonthSalaryDO.class);
        teacherMonthSalaryMapper.updateById(updateObj);
    }

    @Override
    public void deleteTeacherMonthSalary(Long id) {
        // 校验存在
        validateTeacherMonthSalaryExists(id);
        // 删除
        teacherMonthSalaryMapper.deleteById(id);
    }

    private void validateTeacherMonthSalaryExists(Long id) {
        if (teacherMonthSalaryMapper.selectById(id) == null) {
            throw exception(TEACHER_MONTH_SALARY_NOT_EXISTS);
        }
    }

    @Override
    public TeacherMonthSalaryDO getTeacherMonthSalary(Long id) {
        return teacherMonthSalaryMapper.selectById(id);
    }

    @Override
    public PageResult<TeacherMonthSalaryDO> getTeacherMonthSalaryPage(TeacherMonthSalaryPageReqVO pageReqVO) {
        return teacherMonthSalaryMapper.selectPage(pageReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TeacherMonthSalaryRespVO caluateTeacherMonth(String yearMonth, List<Long> personIds) {
        synchronized (CALCULATION_LOCK) {
            if (calculationInProgress) {
                // 如果已经有一个计算任务在执行，判断是否执行超过 30分钟
                long currentTime = System.currentTimeMillis();
                if (currentTime - calculationStartTime < 30 * 60 * 1000) {
                    throw new ServiceException(new ErrorCode(2_100_005, "已有一个计算跟车老师月薪的任务正在执行，请稍后再试"));
                } else {
                    // 如果任务执行超过30分钟，认为之前的任务可能已经异常，重置状态
                    logger.warn("上一个计算任务执行时间超过30分钟，可能已经异常，重置状态");
                    calculationInProgress = false;
                }
            }
            //标记计算任务开始
            calculationInProgress = true;
            calculationStartTime = System.currentTimeMillis();
        }

        try {
            String needMonth = CheckUtil.checkYearMonth(yearMonth, logger);
            List<Long> needPersonIds = new ArrayList<>();

            //构建结果集
            TeacherMonthSalaryRespVO respVO = TeacherMonthSalaryRespVO.builder().createUsernames(new ArrayList<>())
                    .failureUsernames(new LinkedHashMap<>()).build();

            //查询跟车老师的 postId
            PostDO postDb = postMapper.selectByCode("normalfollowcar");
            Long teacherPostId = postDb.getId();

            //如果 personIds为空，则计算所有跟车老师的工资
            if (CollectionUtil.isEmpty(personIds)) {
                if (teacherPostId == null) {
                    throw new ServiceException(new ErrorCode(2_100_004, "未在职务表找到(normalfollowcar)跟车老师的职务信息，无需生成月薪"));
                }

                List<MonthlySalaryEmployeesDO> monthlySalaryEmployeesDOS = monthlySalaryEmployeesMapper.selectList(MonthlySalaryEmployeesDO::getPostId, teacherPostId);

                //如果都为空则抛出未找到合适的人员异常
                if (CollectionUtil.isEmpty(monthlySalaryEmployeesDOS)) {
                    throw new ServiceException(new ErrorCode(2_100_004, "未在月薪人员表找到职务为：跟车老师的人员信息，无需生成月薪"));
                }
                //遍历提取captainEmployeesRespVOS、mentorEmployeesRespVOS中的 Id存到needPersonIds
                for (MonthlySalaryEmployeesDO monthlySalaryEmployeesDO : monthlySalaryEmployeesDOS) {
                    needPersonIds.add(monthlySalaryEmployeesDO.getId());
                }
            } else {
                needPersonIds = personIds;
            }
            if (CollectionUtil.isEmpty(needPersonIds)) {
                throw new ServiceException(new ErrorCode(2_100_004, "未在月薪花名册中找到跟车老师的人员信息，无需生成月薪"));
            }

            //遍历needPersonIds，查询出对应的人员信息，并计算出工资
            for (Long needPersonId : needPersonIds) {
                try {
                    this.handleMonthlySalary(needPersonId, needMonth);
                    respVO.getCreateUsernames().add(needPersonId.toString());
                } catch (Exception e) {
                    logger.info(e.getMessage());
                    respVO.getFailureUsernames().put(needPersonId.toString(), e.getMessage());
                }
            }
            return respVO;
        } finally {
            //无论如何都要重置状态
            synchronized (CALCULATION_LOCK) {
                calculationInProgress = false;
            }
        }
    }

    public TeacherMonthSalaryRespVO handleMonthlySalary(Long personId, String yearMonth) throws Exception {

        /**
         * 所属公司、部门、姓名、身份证号码、联系电话、所属银行、银行卡号、入职时间=月薪人员花名册表
         * 出勤天数汇总=月薪考勤表
         * 油补=油费补贴表
         * 其他费用=其他费用表
         * 正常加班=办公室加班费用表
         * 加班小计=正式加班
         * 缺勤天数=月薪考勤表
         * 缺勤扣款=应发工资÷总天数（出勤➕缺勤➕假期）×缺勤天数
         * 缺勤小计=缺勤扣款
         * 社保=社保表
         * 应发工资=应发工资-缺勤工资+加班+其他-社保=服务金额
         * 实发金额=服务金额
         */

        //根据传来的 yyyy-mm 计算其在这月的开始和结束时间，因为有的基础数据表是精确到日的
        Date yearMonthDate = DateUtil.parse(yearMonth, "yyyy-MM");

        DateTime endOfMonthDateTime = DateUtil.endOfMonth(yearMonthDate);

        String startOfMonthStr = yearMonth + "-01";
        String endOfMonthStr = endOfMonthDateTime.toString("yyyy-MM-dd");

        TeacherMonthSalaryRespVO respVO = new TeacherMonthSalaryRespVO();
        MonthlySalaryEmployeesPageReqVO info = monthlySalaryEmployeesMapper.selectCustomByPersonId(personId);
        if (info == null) {
            throw new ServiceException(new ErrorCode(2_100_004, "未在月薪花名册中找到personId为" + personId + "的人员信息，生成月薪失败"));
        }

        respVO.setCompanyName(info.getCompanyName());
        MonthlySalaryEmployeesDO captain = monthlySalaryEmployeesMapper.selectOne(MonthlySalaryEmployeesDO::getDeptId, info.getDeptId(),
                MonthlySalaryEmployeesDO::getDistrict, info.getDistrict(),
                MonthlySalaryEmployeesDO::getPostId, "captain");
        if (Objects.nonNull(captain)) {
            respVO.setCaptainName(captain.getName());
        }
        respVO.setServiceArea(info.getDistrict());
        respVO.setServiceUnit(info.getAssignedLocation());
        respVO.setEmployeeName(info.getName());
        respVO.setPhone(info.getPhoneNumber());
        respVO.setBankName(info.getBankBranch());
        respVO.setBankCard(info.getBankAccountNumber());
        respVO.setDeptName(info.getDeptName());
        respVO.setPostName(info.getPostName());
        respVO.setPostCode(String.valueOf(info.getPostId()));
        respVO.setPersonId(info.getId());
        respVO.setIdNumber(info.getIdCardNumber());

        // 查询薪酬表
        CaregiverSalaryDO caregiverSalaryDO = caregiverSalaryMapper.selectOne(CaregiverSalaryDO::getPersonId, personId);
        if (caregiverSalaryDO == null) {
            throw new ServiceException(new ErrorCode(2_100_004, "未在跟车员薪酬表中找到人员" + info.getName() + "的信息，生成月薪失败"));
        }

        respVO.setGrossSalary(caregiverSalaryDO.getTotalMonthSalary());
        respVO.setHolidayMonthSalary(caregiverSalaryDO.getHolidayMonthSalary() == null ? BigDecimal.ZERO : caregiverSalaryDO.getHolidayMonthSalary());
        respVO.setPayMonths(caregiverSalaryDO.getSalaryMethod());

        //查询考勤表，计算出出勤天数
        OutEmployeeAttendanceDO outEmployeeAttendanceDO = outEmployeeAttendanceMapper.selectOne(OutEmployeeAttendanceDO::getPersonId, personId
                , OutEmployeeAttendanceDO::getOccurrenceTime, yearMonth);
        if (outEmployeeAttendanceDO == null) {
            throw new ServiceException(new ErrorCode(2_100_004, "未在月薪考勤表中找到人员" + info.getName() + "的信息，生成月薪失败"));
        }
        //出勤天数汇总
        respVO.setAttendanceDays(outEmployeeAttendanceDO.getAttendanceDay() == null ? BigDecimal.ZERO : outEmployeeAttendanceDO.getAttendanceDay());
        //缺勤天数汇总
        respVO.setAbsenceDays(outEmployeeAttendanceDO.getAbsenceDay() == null ? BigDecimal.ZERO : outEmployeeAttendanceDO.getAbsenceDay());
        // 假期
        respVO.setHolidayDays(outEmployeeAttendanceDO.getHolidayDay() == null ? BigDecimal.ZERO : outEmployeeAttendanceDO.getHolidayDay());

        //正常加班费
        OvertimeSubsidyDO overtimeSubsidyDOS = overtimeSubsidyMapper.selectOne(new QueryWrapperX<OvertimeSubsidyDO>().eq("person_id", personId)
                .eq("occurrence_time", yearMonth));
        //如果不为空，汇总overtimePay
        if (overtimeSubsidyDOS != null) {
            //加班费用由本月补贴和加班费组成
            BigDecimal totalOverTimeFee = overtimeSubsidyDOS.getOvertimePay() == null ? BigDecimal.ZERO : overtimeSubsidyDOS.getOvertimePay().add(overtimeSubsidyDOS.getSubsidy() == null ? BigDecimal.ZERO : overtimeSubsidyDOS.getSubsidy());
            respVO.setOvertimeFee(totalOverTimeFee);
        } else {
            respVO.setOvertimeFee(BigDecimal.ZERO);
        }

        //其他费用
        List<OtherFeeDO> otherFeeDOS = otherFeeMapper.selectList(new QueryWrapperX<OtherFeeDO>().eq("drive_phone", info.getPhoneNumber())
                .between("deduct_date", startOfMonthStr, endOfMonthStr)
                .eq("type", OaConstant.SALARY_CATEGORY_MONTHLY));//根据手机号码找到这个人在这个月内的其他费用
        //如果不为空，汇总fee
        if (!CollectionUtil.isEmpty(otherFeeDOS)) {
            //汇总
            BigDecimal otherExpenses = otherFeeDOS.stream().map(OtherFeeDO::getFee).reduce(BigDecimal.ZERO, BigDecimal::add);
            respVO.setOtherExpenses(otherExpenses);
        } else {
            respVO.setOtherExpenses(BigDecimal.ZERO);
        }

        // ==========================公司代缴项=================================

        //社保=社保表
        SocialSecurityDO socialSecurityDO = socialSecurityMapper.selectOne(SocialSecurityDO::getPersonId, personId);
        if (socialSecurityDO != null) {
            respVO.setSocialInsurance(socialSecurityDO.getPersonTotalFee() == null ? BigDecimal.ZERO : socialSecurityDO.getPersonTotalFee());
        } else {
            respVO.setSocialInsurance(BigDecimal.ZERO);
        }

        //公积金=公积金表
        HousingFundDO  housingFundDO = housingFundMapper.selectOne(new QueryWrapperX<HousingFundDO>().eq("person_id", personId));
        if (housingFundDO != null) {
            respVO.setHousingFund(housingFundDO.getIndividualDepositAmount() == null ? BigDecimal.ZERO : housingFundDO.getIndividualDepositAmount());
        } else {
            respVO.setHousingFund(BigDecimal.ZERO);
        }

        //设置薪资所在月份
        respVO.setOccurrenceTime(yearMonth);

        //根据薪酬方式计算薪资
        respVO = handleMonthSalaryByMethod(respVO, caregiverSalaryDO.getSalaryMethod());
        TeacherMonthSalaryDO teacherMonthSalaryDO = teacherMonthSalaryMapper.selectOne(TeacherMonthSalaryDO::getPersonId
                , personId, TeacherMonthSalaryDO::getOccurrenceTime, yearMonth);
        if (Objects.nonNull(teacherMonthSalaryDO)) {
            teacherMonthSalaryMapper.deleteById(teacherMonthSalaryDO);
        }
        int insert = teacherMonthSalaryMapper.insert(BeanUtils.toBean(respVO, TeacherMonthSalaryDO.class));
        if (insert > 0) {
            return respVO;
        } else {
            throw new ServiceException(new ErrorCode(2_100_004, "月薪计算失败,生成" + info.getName() + ",插入失败"));
        }

    }

    public TeacherMonthSalaryRespVO handleMonthSalaryByMethod(TeacherMonthSalaryRespVO teacherMonthSalaryRespVO, String salaryMethod) throws Exception {
        switch (salaryMethod) {
            //传入的薪酬方式为 A
            case SalaryMethodEnum.CODE_A:
                return caluateMethodA(teacherMonthSalaryRespVO);
            case SalaryMethodEnum.CODE_B:
                return caluateMethodB(teacherMonthSalaryRespVO);
            case SalaryMethodEnum.CODE_C:
                return caluateMethodC(teacherMonthSalaryRespVO);
            case SalaryMethodEnum.CODE_D:
                return caluateMethodD(teacherMonthSalaryRespVO);
            case SalaryMethodEnum.CODE_E:
                return caluateMethodE(teacherMonthSalaryRespVO);
            default:
                throw new ServiceException(new ErrorCode(2_100_004, "请选择正确的薪酬方式"));
        }
    }

    /**
     * 计算薪酬方式A
     *应发金额设为 0+加班+其他+
     * @return
     */
    public TeacherMonthSalaryRespVO caluateMethodA(TeacherMonthSalaryRespVO teacherMonthSalaryRespVO) throws Exception {
        //先设服务金额为 0
        teacherMonthSalaryRespVO.setServiceAmount(BigDecimal.ZERO);
         //薪酬方式为 A 的、每年 2 月、8月不计算工资，将应发工资设为 0
        int month = 1+DateUtil.month(DateUtil.parse(teacherMonthSalaryRespVO.getOccurrenceTime(),"yyyy-MM"));
        if(month == 2 || month ==8 ){
            //应发工资为 0
            teacherMonthSalaryRespVO.setGrossSalary(BigDecimal.ZERO);
            BigDecimal serviceFee = teacherMonthSalaryRespVO.getOvertimeFee().add(teacherMonthSalaryRespVO.getOtherExpenses()).subtract(teacherMonthSalaryRespVO.getSocialInsurance().subtract(teacherMonthSalaryRespVO.getHousingFund()));
            teacherMonthSalaryRespVO.setServiceAmount(serviceFee);
        }else{
            //正常月工资
            teacherMonthSalaryRespVO =  calculateNormalServiceAmount(teacherMonthSalaryRespVO);
        }
        return teacherMonthSalaryRespVO;
    }

    /**
     * 计算薪酬方式B
     *
     * @return
     */
    public TeacherMonthSalaryRespVO caluateMethodB(TeacherMonthSalaryRespVO teacherMonthSalaryRespVO) throws Exception {
        int month = 1+DateUtil.month(DateUtil.parse(teacherMonthSalaryRespVO.getOccurrenceTime(),"yyyy-MM"));
        if(month == 2 || month ==8 ){
            teacherMonthSalaryRespVO.setGrossSalary(teacherMonthSalaryRespVO.getHolidayMonthSalary());//2、8取假期工资
        }
        return this.calculateNormalServiceAmount(teacherMonthSalaryRespVO);
    }


    /**
     * 计算薪酬方式C
     *
     * @return
     */
    public TeacherMonthSalaryRespVO caluateMethodC(TeacherMonthSalaryRespVO teacherMonthSalaryRespVO) throws Exception {
          return this.calculateNormalServiceAmount(teacherMonthSalaryRespVO);
    }


    /**
     * 计算薪酬方式D
     *
     * @return
     */
    public TeacherMonthSalaryRespVO caluateMethodD(TeacherMonthSalaryRespVO teacherMonthSalaryRespVO) throws Exception {
        int month = 1+DateUtil.month(DateUtil.parse(teacherMonthSalaryRespVO.getOccurrenceTime(),"yyyy-MM"));
        if(month == 2 || month ==8 ){
            teacherMonthSalaryRespVO.setGrossSalary(teacherMonthSalaryRespVO.getHolidayMonthSalary());//2、8取假期工资
        }
        return this.calculateNormalServiceAmount(teacherMonthSalaryRespVO);
    }

    /**
     * 计算薪酬方式 E
     */
    public TeacherMonthSalaryRespVO caluateMethodE(TeacherMonthSalaryRespVO teacherMonthSalaryRespVO) throws Exception {
        int month = 1+DateUtil.month(DateUtil.parse(teacherMonthSalaryRespVO.getOccurrenceTime(),"yyyy-MM"));
        if(month == 2 || month ==8 ){
            teacherMonthSalaryRespVO.setGrossSalary(teacherMonthSalaryRespVO.getHolidayMonthSalary());//2、8取假期工资
        }
        return this.calculateNormalServiceAmount(teacherMonthSalaryRespVO);
    }

    /**
     * 计算应出勤天数
     */
    public BigDecimal calculateAttendanceDays(TeacherMonthSalaryRespVO teacherMonthSalaryRespVO) {
        return teacherMonthSalaryRespVO.getAbsenceDays().add(teacherMonthSalaryRespVO.getAttendanceDays()).add(teacherMonthSalaryRespVO.getHolidayDays());
    }

    /**
     * 计算缺勤扣款
     */
    public BigDecimal calculateAbsenceDeduction(TeacherMonthSalaryRespVO teacherMonthSalaryRespVO) {
        BigDecimal absencyDeduction = teacherMonthSalaryRespVO.getGrossSalary().divide(calculateAttendanceDays(teacherMonthSalaryRespVO), 2, RoundingMode.HALF_UP).multiply(teacherMonthSalaryRespVO.getAbsenceDays());
        absencyDeduction = absencyDeduction.setScale(0, RoundingMode.HALF_UP);
        return absencyDeduction;
    }

    /**
     * 计算应发工资（由于）
     */
    public TeacherMonthSalaryRespVO calculateNormalServiceAmount(TeacherMonthSalaryRespVO teacherMonthSalaryRespVO) {
        //先设服务金额为 0
        teacherMonthSalaryRespVO.setServiceAmount(BigDecimal.ZERO);
        //取薪酬表的应发工资 -（薪酬表应发工资/应出勤天数*缺勤天数）+加班+其他-社保-公积金
        BigDecimal needPaySalary = teacherMonthSalaryRespVO.getGrossSalary();
        teacherMonthSalaryRespVO.setGrossSalary(needPaySalary);

        //缺勤扣款
        BigDecimal absenceDeduction = calculateAbsenceDeduction(teacherMonthSalaryRespVO);
        teacherMonthSalaryRespVO.setAbsenceDeduction(absenceDeduction);
        //服务金额
        BigDecimal serviceAmount = needPaySalary.subtract(absenceDeduction).add(teacherMonthSalaryRespVO.getOvertimeFee()).add(teacherMonthSalaryRespVO.getOtherExpenses()).subtract(teacherMonthSalaryRespVO.getHousingFund());

        serviceAmount= serviceAmount.setScale(2, RoundingMode.HALF_UP);
        teacherMonthSalaryRespVO.setServiceAmount(serviceAmount);
        return teacherMonthSalaryRespVO;
    }
}