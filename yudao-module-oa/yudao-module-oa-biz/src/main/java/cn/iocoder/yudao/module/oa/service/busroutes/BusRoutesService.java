package cn.iocoder.yudao.module.oa.service.busroutes;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.collection.MapUtils;
import cn.iocoder.yudao.module.oa.controller.admin.busroutes.vo.BusRoutesPageReqVO;
import cn.iocoder.yudao.module.oa.controller.admin.busroutes.vo.BusRoutesRespVO;
import cn.iocoder.yudao.module.oa.controller.admin.busroutes.vo.BusRoutesSaveReqVO;
import cn.iocoder.yudao.module.oa.controller.admin.busroutes.vo.BusRoutesSaveRequestVO;
import cn.iocoder.yudao.module.oa.controller.admin.bustrips.vo.AppBusTripDetail;
import cn.iocoder.yudao.module.oa.dal.dataobject.busroutes.BusRoutesDO;

import javax.validation.Valid;

/**
 * 线路列 Service 接口
 *
 * <AUTHOR>
 */
public interface BusRoutesService {

    /**
     * 创建线路列
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createBusRoutes(@Valid BusRoutesSaveRequestVO createReqVO);

    /**
     * 更新线路列
     *
     * @param updateReqVO 更新信息
     */
    void updateBusRoutes(@Valid BusRoutesSaveRequestVO updateReqVO);

    /**
     * 删除线路列
     *
     * @param id 编号
     */
    void deleteBusRoutes(Long id);

    /**
     * 获得线路列
     *
     * @param id 编号
     * @return 线路列
     */
    BusRoutesSaveRequestVO getBusRoutes(Long id);

    /**
     * 获得线路列分页
     *
     * @param pageReqVO 分页查询
     * @return 线路列分页
     */
    PageResult<BusRoutesRespVO> getBusRoutesPage(BusRoutesPageReqVO pageReqVO);

    AppBusTripDetail selectRouteById(Long routeId);


    PageResult<BusRoutesRespVO> getBusRoutesWaysPage(BusRoutesPageReqVO pageReqVO);
}
