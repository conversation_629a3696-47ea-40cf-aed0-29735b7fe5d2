package cn.iocoder.yudao.module.oa.service.signtemplateattachment;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import cn.iocoder.yudao.module.oa.controller.admin.signtemplateattachment.vo.*;
import cn.iocoder.yudao.module.oa.dal.dataobject.signtemplateattachment.SignTemplateAttachmentDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.oa.dal.mysql.signtemplateattachment.SignTemplateAttachmentMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.oa.enums.ErrorCodeConstants.*;

/**
 * 标牌模板管理关联的文档 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SignTemplateAttachmentServiceImpl implements SignTemplateAttachmentService {

    @Resource
    private SignTemplateAttachmentMapper signTemplateAttachmentMapper;

    @Override
    public Long createSignTemplateAttachment(SignTemplateAttachmentSaveReqVO createReqVO) {
        // 插入
        SignTemplateAttachmentDO signTemplateAttachment = BeanUtils.toBean(createReqVO, SignTemplateAttachmentDO.class);
        signTemplateAttachmentMapper.insert(signTemplateAttachment);
        // 返回
        return signTemplateAttachment.getId();
    }

    @Override
    public void updateSignTemplateAttachment(SignTemplateAttachmentSaveReqVO updateReqVO) {
        // 校验存在
        validateSignTemplateAttachmentExists(updateReqVO.getId());
        // 更新
        SignTemplateAttachmentDO updateObj = BeanUtils.toBean(updateReqVO, SignTemplateAttachmentDO.class);
        signTemplateAttachmentMapper.updateById(updateObj);
    }

    @Override
    public void deleteSignTemplateAttachment(Long id) {
        // 校验存在
        validateSignTemplateAttachmentExists(id);
        // 删除
        signTemplateAttachmentMapper.deleteById(id);
    }

    private void validateSignTemplateAttachmentExists(Long id) {
        if (signTemplateAttachmentMapper.selectById(id) == null) {
            throw exception(SIGN_TEMPLATE_ATTACHMENT_NOT_EXISTS);
        }
    }

    @Override
    public SignTemplateAttachmentDO getSignTemplateAttachment(Long id) {
        return signTemplateAttachmentMapper.selectById(id);
    }

    @Override
    public PageResult<SignTemplateAttachmentDO> getSignTemplateAttachmentPage(SignTemplateAttachmentPageReqVO pageReqVO) {
        return signTemplateAttachmentMapper.selectPage(pageReqVO);
    }

}