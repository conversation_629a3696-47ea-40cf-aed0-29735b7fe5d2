package cn.iocoder.yudao.module.oa.controller.admin.weekattendance.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Schema(description = "管理后台 - 考勤生成 Request VO")
@Data
public class WeekAttendanceCreateReqVO {
    @Schema(description = "周起始日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "周期不能为空")
    private String weekStartDate;

    @Schema(description = "周结束日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "周期不能为空")
    private String weekEndDate;

    @Schema(description = "周数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "周数不能为空")
    private String week;

    @Schema(description = "主键集合")
    private List<Long> ids;
}