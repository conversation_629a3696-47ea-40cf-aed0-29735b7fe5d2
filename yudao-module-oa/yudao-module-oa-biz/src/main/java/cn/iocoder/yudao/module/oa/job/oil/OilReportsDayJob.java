package cn.iocoder.yudao.module.oa.job.oil;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.quartz.core.handler.JobHandler;
import cn.iocoder.yudao.framework.tenant.core.job.TenantJob;
import cn.iocoder.yudao.module.oa.service.fuelreports.FuelReportsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 油耗日月报定时任务
 *
 * <AUTHOR>
 */
@Component("FuelReportsJob") // 显式指定Bean名称为"FuelReportsJob"
@Slf4j
public class OilReportsJob implements JobHandler {

    @Resource
    private FuelReportsService fuelReportsService;

    @Override
    @TenantJob
    public String execute(String param) throws Exception {
        log.info("[execute][油耗日月报定时任务开始，参数：{}]", param);

        try {
            // 解析参数
            String[] parsedParams = parseJobParams(param);
            String startTime = parsedParams[0];
            String endTime = parsedParams[1];
            String vehiIds = parsedParams[2];
            Boolean isMonth = Boolean.parseBoolean(parsedParams[3]);

            log.info("[execute][开始同步油耗日月报数据，开始时间：{}，结束时间：{}，车牌号：{}，是否月报：{}]",
                    startTime, endTime, StrUtil.isBlank(vehiIds) ? "全部" : vehiIds, isMonth);

            // 调用Service层方法进行同步
            String result = fuelReportsService.syncFuelReportsData(startTime, endTime, vehiIds, isMonth);

            log.info("[execute][油耗日月报定时任务结束，结果：{}]", result);
            return result;
        } catch (Exception e) {
            log.error("[execute][油耗日月报定时任务异常]", e);
            return "执行失败：" + e.getMessage();
        }
    }

    /**
     * 解析定时任务参数
     *
     * @param param 参数字符串，格式：开始时间,结束时间,车牌号,是否月报
     * @return 解析后的参数数组 [startTime, endTime, vehiIds, isMonth]
     */
    private String[] parseJobParams(String param) {
        // 默认值
        String startTime = "";
        String endTime = "";
        String vehiIds = "";
        boolean isMonth = false;

        // 如果参数不为空，按逗号分隔获取参数
        if (StrUtil.isNotBlank(param)) {
            String[] params = param.split(",");

            // 安全地获取参数，避免数组越界
            if (params.length > 0) {
                startTime = params[0] == null ? "" : params[0].trim();
            }
            if (params.length > 1) {
                endTime = params[1] == null ? "" : params[1].trim();
            }
            if (params.length > 2) {
                vehiIds = params[2] == null ? "" : params[2].trim();
            }
            if (params.length > 3) {
                String isMonthStr = params[3] == null ? "" : params[3].trim();
                isMonth = "true".equalsIgnoreCase(isMonthStr) || "1".equals(isMonthStr);
            }
        }

        return new String[]{startTime, endTime, vehiIds, String.valueOf(isMonth)};
    }
}
