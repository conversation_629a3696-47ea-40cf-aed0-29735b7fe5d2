package cn.iocoder.yudao.module.oa.controller.admin.housingfund.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDate;
import java.util.*;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

import javax.validation.constraints.NotEmpty;

@Schema(description = "管理后台 - 公积金管理 Response VO")
@Data
@ExcelIgnoreUnannotated
public class HousingFundRespVO {

    @Schema(description = "主表 ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "30817")
    @ExcelProperty("主表 ID")
    private Integer id;

    @Schema(description = "序号", requiredMode = Schema.RequiredMode.REQUIRED, example = "30817")
    @ExcelProperty("序号")
    private Integer routeIndex;

    @Schema(description = "公司名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("公司名称")
    private String companyName;

    @Schema(description = "人员 ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "6547")
    @ExcelProperty("人员 ID")
    private Integer personId;

    @Schema(description = "姓名", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("姓名")
    private String personName;

    @Schema(description = "个人账号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("个人账号")
    private String personalAccount;


    @Schema(description = "个人证件号码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("个人证件号码")
    private String identity;

    @Schema(description = "证件类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "证件类型", converter = DictConvert.class)
    @DictFormat("oa_housingfund_document_type")
    private String documentType;

    @Schema(description = "缴存基数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("缴存基数")
    private BigDecimal depositBase;

    @Schema(description = "公司缴存比例(%)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("公司缴存比例(%)")
    private Integer enterpriseDepositRatio;

    @Schema(description = "个人缴存比例(%)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("个人缴存比例(%)")
    private Integer individualDepositRatio;

    @Schema(description = "单位缴存额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("单位缴存额")
    private BigDecimal enterpriseDepositAmount;

    @Schema(description = "个人缴存额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("个人缴存额")
    private BigDecimal individualDepositAmount;

    @Schema(description = "费用归属月份", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("费用归属月份")
    private String occurrenceTime;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @ExcelProperty(value = "创建时间", format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

}
