package cn.iocoder.yudao.module.oa.service.salarychanges;

import java.util.*;
import javax.validation.*;
import cn.iocoder.yudao.module.oa.controller.admin.salarychanges.vo.*;
import cn.iocoder.yudao.module.oa.dal.dataobject.salarychanges.SalaryChangesDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 薪酬变动 Service 接口
 *
 * <AUTHOR>
 */
public interface SalaryChangesService {

    /**
     * 创建薪酬变动
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSalaryChanges(@Valid SalaryChangesSaveReqVO createReqVO);

    /**
     * 更新薪酬变动
     *
     * @param updateReqVO 更新信息
     */
    void updateSalaryChanges(@Valid SalaryChangesSaveReqVO updateReqVO);

    /**
     * 删除薪酬变动
     *
     * @param id 编号
     */
    void deleteSalaryChanges(Long id);

    /**
     * 获得薪酬变动
     *
     * @param id 编号
     * @return 薪酬变动
     */
    SalaryChangesPageReqVO getSalaryChanges(Long id);

    /**
     * 获得薪酬变动分页
     *
     * @param pageReqVO 分页查询
     * @return 薪酬变动分页
     */
    PageResult<SalaryChangesPageReqVO> getSalaryChangesPage(SalaryChangesPageReqVO pageReqVO);

}
