package cn.iocoder.yudao.module.oa.service.signapply;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.*;
import cn.hutool.http.HttpUtil;
import cn.iocoder.yudao.framework.common.exception.ErrorCode;
import cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils;
import cn.iocoder.yudao.framework.common.util.http.ImageUtils;
import cn.iocoder.yudao.framework.common.util.io.FileUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileConfigDO;
import cn.iocoder.yudao.module.infra.framework.file.core.client.local.LocalFileClientConfig;
import cn.iocoder.yudao.module.infra.service.file.FileConfigService;
import cn.iocoder.yudao.module.infra.service.file.FileService;
import cn.iocoder.yudao.module.oa.controller.admin.signapplyattachment.vo.SignApplyAttachmentRespVO;
import cn.iocoder.yudao.module.oa.controller.admin.signapplydetail.vo.SignApplyDetailRespVO;
import cn.iocoder.yudao.module.oa.controller.admin.signapplyline.vo.SignApplyLineRespVO;
import cn.iocoder.yudao.module.oa.controller.admin.signapplynum.vo.SignApplyNumPrintVO;
import cn.iocoder.yudao.module.oa.controller.admin.signapplynum.vo.SignApplyNumRespVO;
import cn.iocoder.yudao.module.oa.controller.admin.signapplystop.vo.SignApplyStopPrintVO;
import cn.iocoder.yudao.module.oa.controller.admin.signapplystop.vo.SignApplyStopRespVO;
import cn.iocoder.yudao.module.oa.controller.admin.signapplytime.vo.SignApplyTimePrintVO;
import cn.iocoder.yudao.module.oa.controller.admin.signapplytime.vo.SignApplyTimeRespVO;
import cn.iocoder.yudao.module.oa.dal.dataobject.busattachment.BusAttachmentDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.businfo.BusInfoDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.monthlysalaryemployees.MonthlySalaryEmployeesDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.monthlysalarypersonattachment.MonthlySalaryPersonAttachmentDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.personattachment.PersonAttachmentDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.personinfo.PersonInfoDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.signapplyattachment.SignApplyAttachmentDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.signapplydetail.SignApplyDetailDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.signapplyinlog.SignApplyInLogDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.signapplyline.SignApplyLineDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.signapplynum.SignApplyNumDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.signapplystop.SignApplyStopDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.signapplytime.SignApplyTimeDO;
import cn.iocoder.yudao.module.oa.dal.dataobject.signtemplateattachment.SignTemplateAttachmentDO;
import cn.iocoder.yudao.module.oa.dal.mysql.busattachment.BusAttachmentMapper;
import cn.iocoder.yudao.module.oa.dal.mysql.businfo.BusInfoMapper;
import cn.iocoder.yudao.module.oa.dal.mysql.monthlysalaryemployees.MonthlySalaryEmployeesMapper;
import cn.iocoder.yudao.module.oa.dal.mysql.monthlysalarypersonattachment.MonthlySalaryPersonAttachmentMapper;
import cn.iocoder.yudao.module.oa.dal.mysql.personattachment.PersonAttachmentMapper;
import cn.iocoder.yudao.module.oa.dal.mysql.personinfo.PersonInfoMapper;
import cn.iocoder.yudao.module.oa.dal.mysql.signapplyattachment.SignApplyAttachmentMapper;
import cn.iocoder.yudao.module.oa.dal.mysql.signapplydetail.SignApplyDetailMapper;
import cn.iocoder.yudao.module.oa.dal.mysql.signapplyinlog.SignApplyInLogMapper;
import cn.iocoder.yudao.module.oa.dal.mysql.signapplyline.SignApplyLineMapper;
import cn.iocoder.yudao.module.oa.dal.mysql.signapplynum.SignApplyNumMapper;
import cn.iocoder.yudao.module.oa.dal.mysql.signapplystop.SignApplyStopMapper;
import cn.iocoder.yudao.module.oa.dal.mysql.signapplytime.SignApplyTimeMapper;
import cn.iocoder.yudao.module.oa.dal.mysql.signtemplateattachment.SignTemplateAttachmentMapper;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateExceptionHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.docx4j.Docx4J;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import cn.iocoder.yudao.module.oa.controller.admin.signapply.vo.*;
import cn.iocoder.yudao.module.oa.dal.dataobject.signapply.SignApplyDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.oa.dal.mysql.signapply.SignApplyMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.oa.enums.ErrorCodeConstants.*;

/**
 *  标牌申请管理 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class SignApplyServiceImpl implements SignApplyService {
    @Resource
    private SignApplyMapper signApplyMapper;
    @Resource
    private SignApplyDetailMapper signApplyDetailMapper;
    @Resource
    private SignApplyInLogMapper signApplyInLogMapper;
    @Resource
    private SignTemplateAttachmentMapper signTemplateAttachmentMapper;
    @Resource
    private SignApplyAttachmentMapper signApplyAttachmentMapper;
    @Autowired
    private Configuration freemarkerConfig;
    @Resource
    private SignApplyNumMapper signApplyNumMapper;
    @Resource
    private SignApplyLineMapper signApplyLineMapper;
    @Resource
    private SignApplyTimeMapper signApplyTimeMapper;
    @Resource
    private SignApplyStopMapper signApplyStopMapper;
    @Resource
    private MonthlySalaryEmployeesMapper monthlySalaryEmployeesMapper;
    @Resource
    private MonthlySalaryPersonAttachmentMapper monthlySalaryPersonAttachmentMapper;

    @Value("${oa.sign.tempDir}")
    private String signTempDir;
    @Resource
    private FileService fileService;
    @Resource
    private PersonInfoMapper personInfoMapper;
    @Resource
    private PersonAttachmentMapper personAttachmentMapper;
    @Resource
    private BusAttachmentMapper busAttachmentMapper;
    @Resource
    private FileConfigService fileConfigService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long createSignApply(SignApplySaveReqVO createReqVO) {
        // 插入
        SignApplyDO signApply = BeanUtils.toBean(createReqVO, SignApplyDO.class);
        signApply.setSignNo("441900" + LocalDateTimeUtils.getNowString("yyyyMMdd") + RandomUtil.randomNumbers(4));
        SignApplyDetailDO signApplyDetailDO = BeanUtils.toBean(createReqVO.getApplyDetailSaveReqVO(), SignApplyDetailDO.class);
        if(signApplyDetailDO != null){
            signApply.setDriver(signApplyDetailDO.getDriver());
            signApply.setPlateNumber(signApplyDetailDO.getPlateNumber());
            signApply.setSchool(signApplyDetailDO.getFirstParty());
            signApply.setSchoolOwner(signApplyDetailDO.getSchoolOwner());
            signApply.setHandleBusiness("申请");
            if(createReqVO.getApplyDetailSaveReqVO().getLinePictures() != null){
                signApplyDetailDO.setLinePicture(String.join(",", createReqVO.getApplyDetailSaveReqVO().getLinePictures()));
            }
            if(createReqVO.getApplyDetailSaveReqVO().getStopPictures() != null){
                signApplyDetailDO.setStopPicture(String.join(",", createReqVO.getApplyDetailSaveReqVO().getStopPictures()));
            }
        }else{
            signApplyDetailDO = new SignApplyDetailDO();
        }
        signApply.setCreatorName(SecurityFrameworkUtils.getLoginUserNickname());
        if("1".equals(createReqVO.getStatus())){
            signApply.setApplyTime(LocalDateTime.now());
        }
        List<SignTemplateAttachmentDO> signTemplateAttachmentDOList = signTemplateAttachmentMapper.selectList(new LambdaQueryWrapperX<SignTemplateAttachmentDO>().eq(SignTemplateAttachmentDO::getTemplateId, createReqVO.getTemplateId()));
        if(CollectionUtil.isEmpty(signTemplateAttachmentDOList)){
            throw exception(SIGN_TEMPLATE_ATTACHMENT_NOT_EXISTS);
        }
        signApplyMapper.insert(signApply);
        List<SignApplyAttachmentDO> signApplyAttachmentDOList = signTemplateAttachmentDOList.stream().map(template -> {
            SignApplyAttachmentDO signApplyAttachmentDO = BeanUtils.toBean(template, SignApplyAttachmentDO.class);
            signApplyAttachmentDO.setId(null);
            signApplyAttachmentDO.setSignId(signApply.getId());
            return signApplyAttachmentDO;
        }).collect(Collectors.toList());
        signApplyAttachmentMapper.insertBatch(signApplyAttachmentDOList);
        signApplyDetailDO.setSignId(signApply.getId());
        signApplyDetailMapper.insert(signApplyDetailDO);
        if("1".equals(createReqVO.getStatus())) {
            SignApplyInLogDO signApplyInLogDO = new SignApplyInLogDO();
            signApplyInLogDO.setSignId(signApply.getId());
            signApplyInLogDO.setStatus("1");
            signApplyInLogDO.setCreatorName(signApply.getCreatorName());
            signApplyInLogMapper.insert(signApplyInLogDO);
        }
        // 插入校车运行趟次的基本情况列表
        if(CollectionUtil.isNotEmpty(createReqVO.getSignApplyNumSaveReqVO())) {
            createReqVO.getSignApplyNumSaveReqVO().forEach(attachment -> {
                attachment.setSignId(signApply.getId());
            });
            signApplyNumMapper.insertBatch(BeanUtils.toBean(createReqVO.getSignApplyNumSaveReqVO(), SignApplyNumDO.class));
        }
        // 插入校车运行线路情况列表
        if(CollectionUtil.isNotEmpty(createReqVO.getSignApplyLineSaveReqVO())) {
            createReqVO.getSignApplyLineSaveReqVO().forEach(attachment -> {
                attachment.setSignId(signApply.getId());
            });
            signApplyLineMapper.insertBatch(BeanUtils.toBean(createReqVO.getSignApplyLineSaveReqVO(), SignApplyLineDO.class));
        }
        // 插入校车运行时间情况列表
        if(CollectionUtil.isNotEmpty(createReqVO.getSignApplyTimeSaveReqVO())) {
            createReqVO.getSignApplyTimeSaveReqVO().forEach(attachment -> {
                attachment.setSignId(signApply.getId());
            });
            signApplyTimeMapper.insertBatch(BeanUtils.toBean(createReqVO.getSignApplyTimeSaveReqVO(), SignApplyTimeDO.class));
        }
        // 插入停靠站点情况列表
        if(CollectionUtil.isNotEmpty(createReqVO.getSignApplyStopSaveReqVO())) {
            createReqVO.getSignApplyStopSaveReqVO().forEach(attachment -> {
                attachment.setSignId(signApply.getId());
            });
            signApplyStopMapper.insertBatch(BeanUtils.toBean(createReqVO.getSignApplyStopSaveReqVO(), SignApplyStopDO.class));
        }
        // 返回
        return signApply.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateSignApply(SignApplySaveReqVO updateReqVO) {
        SignApplyDO signApplyDO = signApplyMapper.selectById(updateReqVO.getId());
        if (signApplyDO == null) {
            throw exception(SIGN_APPLY_NOT_EXISTS);
        }
        if("1".equals(signApplyDO.getStatus()) && "1".equals(updateReqVO.getStatus())){
            throw exception(SIGN_APPLY_HAS_SUBMIT);
        }
        // 更新
        SignApplyDO updateObj = BeanUtils.toBean(updateReqVO, SignApplyDO.class);
        SignApplyDetailDO signApplyDetailDO = BeanUtils.toBean(updateReqVO.getApplyDetailSaveReqVO(), SignApplyDetailDO.class);
        if(signApplyDetailDO != null){
            updateObj.setDriver(signApplyDetailDO.getDriver());
            updateObj.setPlateNumber(signApplyDetailDO.getPlateNumber());
            updateObj.setSchool(signApplyDetailDO.getFirstParty());
            updateObj.setSchoolOwner(signApplyDetailDO.getSchoolOwner());
            if(updateReqVO.getApplyDetailSaveReqVO().getLinePictures() != null){
                signApplyDetailDO.setLinePicture(String.join(",", updateReqVO.getApplyDetailSaveReqVO().getLinePictures()));
            }
            if(updateReqVO.getApplyDetailSaveReqVO().getStopPictures() != null){
                signApplyDetailDO.setStopPicture(String.join(",", updateReqVO.getApplyDetailSaveReqVO().getStopPictures()));
            }
        }
        if("1".equals(updateReqVO.getStatus())){
            updateObj.setApplyTime(LocalDateTime.now());
        }
        signApplyMapper.updateById(updateObj);
        signApplyDetailMapper.updateById(signApplyDetailDO);
        if("1".equals(updateReqVO.getStatus())) {
            SignApplyInLogDO signApplyInLogDO = new SignApplyInLogDO();
            signApplyInLogDO.setSignId(updateObj.getId());
            signApplyInLogDO.setStatus("1");
            signApplyInLogDO.setCreatorName(updateObj.getCreatorName());
            signApplyInLogMapper.insert(signApplyInLogDO);
        }
        signApplyNumMapper.delete(SignApplyNumDO::getSignId,updateReqVO.getId());
        // 插入校车运行趟次的基本情况列表
        if(CollectionUtil.isNotEmpty(updateReqVO.getSignApplyNumSaveReqVO())) {
            updateReqVO.getSignApplyNumSaveReqVO().forEach(attachment -> {
                attachment.setId(null);
                attachment.setSignId(updateReqVO.getId());
            });
            signApplyNumMapper.insertBatch(BeanUtils.toBean(updateReqVO.getSignApplyNumSaveReqVO(), SignApplyNumDO.class));
        }
        signApplyLineMapper.delete(SignApplyLineDO::getSignId,updateReqVO.getId());
        // 插入校车运行线路情况列表
        if(CollectionUtil.isNotEmpty(updateReqVO.getSignApplyLineSaveReqVO())) {
            updateReqVO.getSignApplyLineSaveReqVO().forEach(attachment -> {
                attachment.setId(null);
                attachment.setSignId(updateReqVO.getId());
            });
            signApplyLineMapper.insertBatch(BeanUtils.toBean(updateReqVO.getSignApplyLineSaveReqVO(), SignApplyLineDO.class));
        }
        signApplyTimeMapper.delete(SignApplyTimeDO::getSignId,updateReqVO.getId());
        // 插入校车运行时间情况列表
        if(CollectionUtil.isNotEmpty(updateReqVO.getSignApplyTimeSaveReqVO())) {
            updateReqVO.getSignApplyTimeSaveReqVO().forEach(attachment -> {
                attachment.setId(null);
                attachment.setSignId(updateReqVO.getId());
            });
            signApplyTimeMapper.insertBatch(BeanUtils.toBean(updateReqVO.getSignApplyTimeSaveReqVO(), SignApplyTimeDO.class));
        }
        signApplyStopMapper.delete(SignApplyStopDO::getSignId,updateReqVO.getId());
        // 插入停靠站点情况列表
        if(CollectionUtil.isNotEmpty(updateReqVO.getSignApplyStopSaveReqVO())) {
            updateReqVO.getSignApplyStopSaveReqVO().forEach(attachment -> {
                attachment.setId(null);
                attachment.setSignId(updateReqVO.getId());
            });
            signApplyStopMapper.insertBatch(BeanUtils.toBean(updateReqVO.getSignApplyStopSaveReqVO(), SignApplyStopDO.class));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteSignApply(Long id) {
        // 校验存在
        validateSignApplyExists(id);
        // 删除
        signApplyMapper.deleteById(id);
        signApplyAttachmentMapper.delete(SignApplyAttachmentDO::getSignId,id);
        signApplyDetailMapper.delete(SignApplyDetailDO::getSignId,id);
        signApplyNumMapper.delete(SignApplyNumDO::getSignId,id);
        signApplyLineMapper.delete(SignApplyLineDO::getSignId,id);
        signApplyTimeMapper.delete(SignApplyTimeDO::getSignId,id);
        signApplyStopMapper.delete(SignApplyStopDO::getSignId,id);
    }

    private void validateSignApplyExists(Long id) {
        if (signApplyMapper.selectById(id) == null) {
            throw exception(SIGN_APPLY_NOT_EXISTS);
        }
    }

    @Override
    public SignApplyDO getSignApply(Long id) {
        return signApplyMapper.selectById(id);
    }

    @Override
    public PageResult<SignApplyDO> getSignApplyPage(SignApplyPageReqVO pageReqVO) {
        return signApplyMapper.selectPage(pageReqVO);
    }

    @Override
    public String previewDocTemplate(Long id, HttpServletResponse response) throws Exception{
        SignApplyAttachmentDO signApplyAttachmentDO = signApplyAttachmentMapper.selectById(id);
        if(signApplyAttachmentDO == null){
            throw exception(SIGN_APPLY_ATTACHMENT_NOT_EXISTS);
        }
        if(StringUtils.isNotEmpty(signApplyAttachmentDO.getUploadPath())){
            return signApplyAttachmentDO.getUploadPath();
        }
        if("2".equals(signApplyAttachmentDO.getCategory())){
            freemarkerConfig.setEncoding(Locale.getDefault(), "utf-8");
            freemarkerConfig.setTemplateExceptionHandler(TemplateExceptionHandler.IGNORE_HANDLER);
            Template template = freemarkerConfig.getTemplate(signApplyAttachmentDO.getName() + ".xml","utf-8");
    /*      response.setHeader("Content-Disposition", "attachment; filename=document."+signApplyAttachmentDO.getType());
            response.setCharacterEncoding("utf-8");
            if("doc".equals(signApplyAttachmentDO.getType())) {
                response.setContentType("application/msword");
            }else if("xls".equals(signApplyAttachmentDO.getType())){
                response.setContentType("application/msexcel");
            }
            template.process(getPrintData(signApplyAttachmentDO.getSignId()), response.getWriter());*/
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            Writer out = new OutputStreamWriter(outputStream, StandardCharsets.UTF_8);
            template.process(getPrintData(signApplyAttachmentDO.getSignId()), out);
            out.flush(); // 刷新输出流，确保所有数据都被写出
            byte[] byteArray = outputStream.toByteArray(); // 将输出流转为字节数组
            out.close(); // 关闭流
            outputStream.close(); // 关闭流
            String url = fileService.createFile(signApplyAttachmentDO.getName(),"view/"+ IdUtil.fastUUID() + "." + signApplyAttachmentDO.getType(), byteArray);
            // 获取请求的路径
            String path = StrUtil.subAfter(url, "/get/", false);
            path = URLUtil.decode(path);
            FileConfigDO config = fileConfigService.getFileConfig(24L);
            String basePath = ((LocalFileClientConfig) config.getConfig()).getBasePath();
            String filePath = basePath + path;
            String docxPath = filePath.replaceAll("(\\.docx)|(\\.doc)", ".docx");
            try (FileInputStream inputStream = new FileInputStream(filePath);) {
                WordprocessingMLPackage wmlPackage = Docx4J.load(inputStream);
                //转换为DOCX
                try (FileOutputStream docx = new FileOutputStream(docxPath)) {
                    Docx4J.save(wmlPackage, docx, Docx4J.FLAG_SAVE_ZIP_FILE);
                }
            } catch (Exception e) {
                log.error("预览文档doc转docx异常{}", e.getMessage());
            }
            return url.replaceAll("(\\.docx)|(\\.doc)", ".docx");
        }else{
            SignApplyDetailDO signApplyDetailDO = signApplyDetailMapper.selectOne(SignApplyDetailDO::getSignId, signApplyAttachmentDO.getSignId());
            if(signApplyDetailDO == null){
                throw exception(new ErrorCode(2_100_004, "标牌申请信息不存在"));
            }
            if("4".equals(signApplyAttachmentDO.getCategory())) {//人员附件
                if(StringUtils.isEmpty(signApplyDetailDO.getMobile())){
                    throw exception(new ErrorCode(2_100_004, "联系电话未填写，无法查看该附件"));
                }
                MonthlySalaryEmployeesDO monthlySalaryEmployeesDO = monthlySalaryEmployeesMapper.selectByPhoneNumber(signApplyDetailDO.getMobile());
                PersonInfoDO personInfoDO = personInfoMapper.selectOne(PersonInfoDO::getMobile, signApplyDetailDO.getMobile());
                if(personInfoDO == null && monthlySalaryEmployeesDO == null){
                    throw exception(new ErrorCode(2_100_004, "人员花名册不存在该电话：" + signApplyDetailDO.getMobile()));
                }
                if(monthlySalaryEmployeesDO != null){
                    List<MonthlySalaryPersonAttachmentDO> monthlySalaryPersonAttachmentList = monthlySalaryPersonAttachmentMapper.selectList(MonthlySalaryPersonAttachmentDO::getPersonId, monthlySalaryEmployeesDO.getId(), MonthlySalaryPersonAttachmentDO::getAttachmentName, signApplyAttachmentDO.getName());
                    if(CollectionUtil.isNotEmpty(monthlySalaryPersonAttachmentList) && StringUtils.isNotEmpty(monthlySalaryPersonAttachmentList.get(0).getUploadPath())){
                        return monthlySalaryPersonAttachmentList.get(0).getUploadPath();
                    }
                }
                if(personInfoDO != null){
                    List<PersonAttachmentDO> personAttachmentDOList = personAttachmentMapper.selectList(PersonAttachmentDO::getPersonId, personInfoDO.getId(), PersonAttachmentDO::getAttachmentName, signApplyAttachmentDO.getName());
                    if(CollectionUtil.isNotEmpty(personAttachmentDOList) && StringUtils.isNotEmpty(personAttachmentDOList.get(0).getUploadPath())){
                        return personAttachmentDOList.get(0).getUploadPath();
                    }
                }
                throw exception(new ErrorCode(2_100_004, "该人员附件未上传，无法查看"));
            }else if("3".equals(signApplyAttachmentDO.getCategory()) || "5".equals(signApplyAttachmentDO.getCategory())){//车辆、保险
                if(StringUtils.isEmpty(signApplyDetailDO.getPlateNumber())){
                    throw exception(new ErrorCode(2_100_004, "车牌号码未填写，无法查看该附件"));
                }
                List<BusAttachmentDO> busAttachmentDOList = busAttachmentMapper.selectList(BusAttachmentDO::getPlateNumber, signApplyDetailDO.getPlateNumber(),BusAttachmentDO::getAttachmentName,signApplyAttachmentDO.getName());
                if(CollectionUtil.isNotEmpty(busAttachmentDOList) && StringUtils.isNotEmpty(busAttachmentDOList.get(0).getUploadPath())){
                    return busAttachmentDOList.get(0).getUploadPath();
                }
                throw exception(new ErrorCode(2_100_004, "该车辆附件未上传，无法查看"));
            }
        }
        return "";
    }

    public SignApplyPrintVO getPrintData(Long signId){
        SignApplyDetailDO signApplyDetailDO = signApplyDetailMapper.selectOne(SignApplyDetailDO::getSignId, signId);
        SignApplyPrintVO signApplyPrintVO = BeanUtils.toBean(signApplyDetailDO, SignApplyPrintVO.class);
        if(signApplyPrintVO != null){
            //运行趟次
            List<SignApplyNumDO> signApplyNumDOList = signApplyNumMapper.selectList(SignApplyNumDO::getSignId, signId);
            List<SignApplyNumPrintVO> printVOList = signApplyNumDOList.stream().map(num -> {
                SignApplyNumPrintVO printVO = BeanUtils.toBean(num, SignApplyNumPrintVO.class);
                if(StringUtils.isNotEmpty(printVO.getStudent())){
                    printVO.setStudentNum(printVO.getStudent().split("[,，、]").length);
                }
                return printVO;
            }).collect(Collectors.toList());
            signApplyPrintVO.setSignApplyNumList(printVOList);
            //运行线路
            List<SignApplyLineDO> signApplyLineDOList = signApplyLineMapper.selectList(SignApplyLineDO::getSignId, signId);
            signApplyPrintVO.setSignApplyLineList(signApplyLineDOList);
            Set<String> passRoads = new HashSet<>();
            Set<String> passTowns = new HashSet<>();
            signApplyLineDOList.forEach(line->{
                passRoads.add(line.getPassRoad());
                passTowns.add(line.getPassTowm());
            });
            if(CollectionUtil.isNotEmpty(passRoads)){
                signApplyPrintVO.setPassRoads(String.join("、", passRoads));
                signApplyPrintVO.setPassRoadNum(passRoads.size());
            }
            if (!passTowns.isEmpty()) {
                StringBuilder passTownsStr = new StringBuilder();
                for (String passTown : passTowns) {
                    passTownsStr.append(passTown).append("、");
                }
                signApplyPrintVO.setPassTowns(passTownsStr.substring(0, passTownsStr.length() - 1));
            }
            //运行时间
            Set<String> departureDateSet = new HashSet<>();//开行时间
            Set<String> timePassRoadSet = new HashSet<>();//行驶线路
            Set<String> dockingStationSet = new HashSet<>();//停靠站点
            List<SignApplyTimeDO> signApplyTimeDOList = signApplyTimeMapper.selectList(SignApplyTimeDO::getSignId, signId);
            List<SignApplyTimePrintVO> signApplyTimePrintList = signApplyTimeDOList.stream().map(time -> {
                SignApplyTimePrintVO signApplyTimePrintVO = BeanUtils.toBean(time, SignApplyTimePrintVO.class);
                if(StringUtils.isNotEmpty(signApplyTimePrintVO.getStartTime()) && StringUtils.isNotEmpty(signApplyTimePrintVO.getEndTime())){
                    String runNum = "";
                    if(time.getRunNum().length()>2){
                        runNum = time.getRunNum().substring(0,2);
                    }
                    String startTime = signApplyTimePrintVO.getStartTime().replace("时",":").replace("分","");
                    String endTime = signApplyTimePrintVO.getEndTime().replace("时",":").replace("分","");
                    signApplyTimePrintVO.setDepartureDate(runNum + ":" + startTime + "-" + endTime);
                    departureDateSet.add(signApplyTimePrintVO.getDepartureDate());

                }
                if(signApplyTimePrintVO.getTimeInterval()!=null && signApplyTimePrintVO.getTimeInterval().indexOf("（") > 0){
                    signApplyTimePrintVO.setPassRoad(signApplyTimePrintVO.getTimeInterval().substring(signApplyTimePrintVO.getTimeInterval().indexOf("（")+1,signApplyTimePrintVO.getTimeInterval().lastIndexOf("）")));
                    timePassRoadSet.add(signApplyTimePrintVO.getPassRoad());
                }
                if(signApplyTimePrintVO.getStopInfo()!=null){
                    if(signApplyTimePrintVO.getStopInfo().indexOf("在")>=0){
                        signApplyTimePrintVO.setDockingStation(signApplyTimePrintVO.getStopInfo().substring(signApplyTimePrintVO.getStopInfo().indexOf("在")+1,signApplyTimePrintVO.getStopInfo().indexOf("站点")));
                        dockingStationSet.add(signApplyTimePrintVO.getDockingStation());
                    }else{
                        signApplyTimePrintVO.setDockingStation(signApplyTimePrintVO.getStopInfo());
                    }
                }
                //图片转base64
                if(StringUtils.isNotEmpty(signApplyTimePrintVO.getStopInfoImg())){
                    signApplyTimePrintVO.setStopInfoImg(ImageUtils.convertImageUrlToBase64(signApplyTimePrintVO.getStopInfoImg()));
                }
                return signApplyTimePrintVO;
            }).collect(Collectors.toList());
            //运行线路详情表处理
            Integer runNumber = 0;
            for (SignApplyTimePrintVO time : signApplyTimePrintList) {
                if (time.getLevel() == 1) {
                    runNumber = runNumber + 1;
                    time.setChildNumber(signApplyTimePrintList.stream().filter(childTime -> Objects.equals(childTime.getParentId(), time.getIdent())).collect(Collectors.toList()).size());
                }
                time.setRunNumber(runNumber);
            }
            signApplyPrintVO.setSignApplyTimeList(signApplyTimePrintList);
            if(CollectionUtil.isNotEmpty(timePassRoadSet)){
                signApplyPrintVO.setDrivingRoute(String.join("、", timePassRoadSet));
            }
            if(CollectionUtil.isNotEmpty(dockingStationSet)){
                signApplyPrintVO.setDockingStation(String.join("、", dockingStationSet));
            }
            if(CollectionUtil.isNotEmpty(departureDateSet)){
                signApplyPrintVO.setDepartureDate(String.join("  ", departureDateSet));
            }
            //停靠站点情况
            List<SignApplyStopDO> signApplyStopDOList = signApplyStopMapper.selectList(SignApplyStopDO::getSignId, signId);
            List<SignApplyStopPrintVO> signApplyStopPrintDOList = signApplyStopDOList.stream().map(stop -> {
                SignApplyStopPrintVO stopPrintVO = BeanUtils.toBean(stop, SignApplyStopPrintVO.class);
                if (StringUtils.isNotEmpty(stopPrintVO.getStopPicture())) {
                    List<String> pictureList = new ArrayList<>();
                    for (String pictrue : stopPrintVO.getStopPicture().split(",")) {
                        pictureList.add(ImageUtils.convertImageUrlToBase64(pictrue));
                    }
                    stopPrintVO.setStopPictureList(pictureList);
                }
                return stopPrintVO;
            }).collect(Collectors.toList());
            signApplyPrintVO.setSignApplyStopList(signApplyStopPrintDOList);
            //照管人
            if(StringUtils.isNotEmpty(signApplyPrintVO.getCaretaker())){
                if(StringUtils.isNotEmpty(signApplyPrintVO.getCaretaker2())){
                    signApplyPrintVO.setCaretakerName(signApplyPrintVO.getCaretaker() + "、" + signApplyPrintVO.getCaretaker2());
                }else{
                    signApplyPrintVO.setCaretakerName(signApplyPrintVO.getCaretaker());
                }
            }else{
                signApplyPrintVO.setCaretakerName(signApplyPrintVO.getCaretaker2());
            }
            //备用驾驶员信息
            if(StringUtils.isNotEmpty(signApplyPrintVO.getBackupPhone())){
                MonthlySalaryEmployeesDO monthlySalaryEmployeesDO = monthlySalaryEmployeesMapper.selectByPhoneNumber(signApplyPrintVO.getBackupPhone());
                if(monthlySalaryEmployeesDO != null){
                    signApplyPrintVO.setBackupSex(monthlySalaryEmployeesDO.getSex() == 2 ? "女":"男");
                    if(monthlySalaryEmployeesDO.getDateOfBirth() != null){
                        try {
                            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                            String dateOfBirth = monthlySalaryEmployeesDO.getDateOfBirth().format(df);
                            signApplyPrintVO.setBackupAge(DateUtil.ageOfNow(dateOfBirth));
                        }catch (Exception ignored){}
                    }
                    signApplyPrintVO.setBackupAllowModel(monthlySalaryEmployeesDO.getDrivingLicenses());
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");
                    if(monthlySalaryEmployeesDO.getSchoolBusQualificationDate() != null){
                        signApplyPrintVO.setBackupAcquireDate(monthlySalaryEmployeesDO.getSchoolBusQualificationDate().format(formatter));
                    }
                    if(monthlySalaryEmployeesDO.getDriverLicenseExpiryDateEnd() != null){
                        signApplyPrintVO.setBackupDirverValidDate(monthlySalaryEmployeesDO.getDriverLicenseExpiryDateEnd().format(formatter));
                    }
                }else{
                    PersonInfoDO personInfoDO = personInfoMapper.selectByPhone(signApplyPrintVO.getBackupPhone());
                    if(personInfoDO != null){
                        signApplyPrintVO.setBackupSex(personInfoDO.getSex() == 2 ? "女":"男");
                        signApplyPrintVO.setBackupAllowModel(personInfoDO.getDriveModel());
                        try {
                            signApplyPrintVO.setBackupAge(DateUtil.ageOfNow(personInfoDO.getBirthdate()));
                        }catch (Exception e){}
                        if(StringUtils.isNotEmpty(personInfoDO.getBusDriveDate())){
                            String[] busDriveDates = personInfoDO.getBusDriveDate().split("-");
                            if(busDriveDates.length == 3){
                                signApplyPrintVO.setBackupAcquireDate(busDriveDates[0] + "年" + busDriveDates[1] + "月" + busDriveDates[2] + "日");
                            }
                        }
                        if(StringUtils.isNotEmpty(personInfoDO.getDriverLicenseEnd())){
                            String[] driverLicenseEnds = personInfoDO.getDriverLicenseEnd().split("-");
                            if(driverLicenseEnds.length == 3){
                                signApplyPrintVO.setBackupDirverValidDate(driverLicenseEnds[0] + "年" + driverLicenseEnds[1] + "月" + driverLicenseEnds[2] + "日");
                            }
                        }
                    }
                }
                if("2999-12-31".equals(signApplyPrintVO.getBackupDirverValidDate()) || "2999年12月31日".equals(signApplyPrintVO.getBackupDirverValidDate())){
                    signApplyPrintVO.setBackupDirverValidDate("长期");
                }
            }
            //责任书有效期
            if(StringUtils.isNotEmpty(signApplyPrintVO.getResponsibilityDate())){
                signApplyPrintVO.setResponsibilityDate(signApplyPrintVO.getResponsibilityDate().replaceAll("\"","").replace("[","").replace("]","").replace(",","至"));
            }
            //核载人数
            if(signApplyPrintVO.getLoadNumber()>=19){
                signApplyPrintVO.setAdultNumber(3);
            }else{
                signApplyPrintVO.setAdultNumber(2);
            }
            signApplyPrintVO.setStudentNumber(signApplyPrintVO.getLoadNumber()-signApplyPrintVO.getAdultNumber());
            //驾驶证审验有效期
            if("2999-12-31".equals(signApplyPrintVO.getDirverValidDate()) || "2999年12月31日".equals(signApplyPrintVO.getDirverValidDate())){
                signApplyPrintVO.setDirverValidDate("长期");
            }
            //运行方案运行线路情况
            Map<String,List<SignApplyLineDO>> signApplyLineMap = new HashMap<>();
            List<SignApplyLineDO> parentApplyLineList = signApplyLineDOList.stream().filter(line -> line.getLevel() == 1L).collect(Collectors.toList());
            for(int i = 0;i<parentApplyLineList.size();i++){
                int finalI = i;
                List<SignApplyLineDO> signApplyLineList = new ArrayList<>();
                signApplyLineList.add(parentApplyLineList.get(finalI));
                List<SignApplyLineDO> childApplyLineList = signApplyLineDOList.stream().filter(line -> Objects.equals(line.getParentId(), parentApplyLineList.get(finalI).getIdent())).collect(Collectors.toList());
                signApplyLineList.addAll(childApplyLineList);
                signApplyLineMap.put(String.valueOf(finalI+1),signApplyLineList);
            }
            signApplyPrintVO.setSignApplyLineMap(signApplyLineMap);
            //运行方案运行时间情况
            Map<String,List<SignApplyTimePrintVO>> signApplyTimeMap = new HashMap<>();
            List<SignApplyTimePrintVO> parentApplyTimeList = signApplyPrintVO.getSignApplyTimeList().stream().filter(time -> time.getLevel() == 1L).collect(Collectors.toList());
            for(int i = 0;i<parentApplyTimeList.size();i++){
                int finalI = i;
                List<SignApplyTimePrintVO> signApplyTimeList = new ArrayList<>();
                signApplyTimeList.add(parentApplyTimeList.get(finalI));
                List<SignApplyTimePrintVO> childApplyTimeList = signApplyPrintVO.getSignApplyTimeList().stream().filter(time -> Objects.equals(time.getParentId(), parentApplyTimeList.get(finalI).getIdent())).collect(Collectors.toList());
                signApplyTimeList.addAll(childApplyTimeList);
                signApplyTimeMap.put(String.valueOf(finalI+1),signApplyTimeList);
            }
            signApplyPrintVO.setSignApplyTimeMap(signApplyTimeMap);
        }
        return signApplyPrintVO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateSignApplyStatus(SignApplyUpdateStatusVO updateStatusVO) {
        SignApplyDO signApplyDO = signApplyMapper.selectById(updateStatusVO.getId());
        if (signApplyDO == null) {
            throw exception(SIGN_APPLY_NOT_EXISTS);
        }
        signApplyDO.setStatus(updateStatusVO.getStatus());
        signApplyMapper.updateById(signApplyDO);
        SignApplyInLogDO signApplyInLogDO = new SignApplyInLogDO();
        signApplyInLogDO.setSignId(signApplyDO.getId());
        signApplyInLogDO.setStatus(updateStatusVO.getStatus());
        signApplyInLogDO.setCreatorName(signApplyDO.getCreatorName());
        signApplyInLogDO.setRemark(updateStatusVO.getRemark());
        signApplyInLogDO.setUpdaterName(SecurityFrameworkUtils.getLoginUserNickname());
        signApplyInLogMapper.insert(signApplyInLogDO);
    }

    @Override
    public SignApplyRespVO getSignApplyDetail(Long id) {
        SignApplyDO signApplyDO = signApplyMapper.selectById(id);
        SignApplyRespVO signApplyRespVO = BeanUtils.toBean(signApplyDO, SignApplyRespVO.class);
        if(signApplyRespVO != null){
            SignApplyDetailRespVO signApplyDetailRespVO = BeanUtils.toBean(signApplyDetailMapper.selectOne(SignApplyDetailDO::getSignId, id), SignApplyDetailRespVO.class);
            signApplyRespVO.setApplyDetailSaveReqVO(signApplyDetailRespVO);
            List<SignApplyAttachmentRespVO> signApplyAttachmentRespVOlist = BeanUtils.toBean(signApplyAttachmentMapper.selectList(SignApplyAttachmentDO::getSignId, id), SignApplyAttachmentRespVO.class);
            signApplyRespVO.setSignApplyAttachmentRespVOList(signApplyAttachmentRespVOlist);
            List<SignApplyNumRespVO> signApplyNumRespVOList = BeanUtils.toBean(signApplyNumMapper.selectList(SignApplyNumDO::getSignId, id), SignApplyNumRespVO.class);
            signApplyRespVO.setSignApplyNumSaveReqVO(signApplyNumRespVOList);
            List<SignApplyLineRespVO> signApplyLineRespVOList = BeanUtils.toBean(signApplyLineMapper.selectList(SignApplyLineDO::getSignId, id), SignApplyLineRespVO.class);
            signApplyRespVO.setSignApplyLineSaveReqVO(signApplyLineRespVOList);
            List<SignApplyTimeRespVO> signApplyTimeRespVOList = BeanUtils.toBean(signApplyTimeMapper.selectList(SignApplyTimeDO::getSignId, id), SignApplyTimeRespVO.class);
            signApplyRespVO.setSignApplyTimeSaveReqVO(signApplyTimeRespVOList);
            List<SignApplyStopRespVO> signApplyStopRespVOList = BeanUtils.toBean(signApplyStopMapper.selectList(SignApplyStopDO::getSignId, id), SignApplyStopRespVO.class);
            signApplyRespVO.setSignApplyStopSaveReqVO(signApplyStopRespVOList);
        }
        return signApplyRespVO;
    }

    @Override
    public void archiveSignApply(SignApplyArchiveStatusVO signApplyArchiveStatusVO) {
        SignApplyDO signApplyDO = signApplyMapper.selectById(signApplyArchiveStatusVO.getId());
        if (signApplyDO == null) {
            throw exception(SIGN_APPLY_NOT_EXISTS);
        }
        if(!"3".equals(signApplyDO.getStatus())){
            throw exception(SIGN_APPLY_NOT_FINISH);
        }
        signApplyDO.setStartDate(signApplyArchiveStatusVO.getStartDate());
        signApplyDO.setEndDate(signApplyArchiveStatusVO.getEndDate());
        signApplyDO.setSignReleaseTime(signApplyArchiveStatusVO.getSignReleaseTime());
        signApplyDO.setSignNumber(signApplyArchiveStatusVO.getSignNumber());
        signApplyDO.setIssuingAuthority(signApplyArchiveStatusVO.getIssuingAuthority());
        signApplyDO.setSignUrl(signApplyArchiveStatusVO.getSignUrl());
        signApplyDO.setProcessStatus("已完成");
        signApplyMapper.updateById(signApplyDO);
    }

    @Override
    public void exportZip(HttpServletResponse response, SignApplyPageReqVO pageReqVO) {
        List<SignApplyAttachmentDO> signApplyAttachmentDOList = signApplyAttachmentMapper.selectList(SignApplyAttachmentDO::getSignId, pageReqVO.getId());
        String outTempDir = signTempDir + UUID.fastUUID();
        File fileDir = new File(outTempDir);
        if(!fileDir.exists()){
            fileDir.mkdirs();
        }
        try {
            SignApplyPrintVO printData = getPrintData(pageReqVO.getId());
            SignApplyDetailDO signApplyDetailDO = signApplyDetailMapper.selectOne(SignApplyDetailDO::getSignId, pageReqVO.getId());
            if(signApplyDetailDO == null){
                return;
            }
            PersonInfoDO personInfoDO = personInfoMapper.selectOne(PersonInfoDO::getMobile, signApplyDetailDO.getMobile());
            List<PersonAttachmentDO> personAttachmentList;
            if(personInfoDO != null){
                personAttachmentList = personAttachmentMapper.selectList(PersonAttachmentDO::getPersonId, personInfoDO.getId());
            } else {
                personAttachmentList = new ArrayList<>();
            }
            MonthlySalaryEmployeesDO monthlySalaryEmployeesDO = monthlySalaryEmployeesMapper.selectByPhoneNumber(signApplyDetailDO.getMobile());
            List<MonthlySalaryPersonAttachmentDO> monthlyPersonAttachmentList;
            if(monthlySalaryEmployeesDO != null){
                monthlyPersonAttachmentList = monthlySalaryPersonAttachmentMapper.selectList(MonthlySalaryPersonAttachmentDO::getPersonId, monthlySalaryEmployeesDO.getId());
            } else {
                monthlyPersonAttachmentList = new ArrayList<>();
            }
            List<BusAttachmentDO> busAttachmentDOList = busAttachmentMapper.selectList(BusAttachmentDO::getPlateNumber, signApplyDetailDO.getPlateNumber());
            signApplyAttachmentDOList.forEach(attachmentDO->{
                 try {
                     if("2".equals(attachmentDO.getCategory())) {//自定义
                         if(StringUtils.isNotEmpty(attachmentDO.getUploadPath())){
                             String uploadPath = attachmentDO.getUploadPath();
                             HttpUtil.downloadFile(attachmentDO.getUploadPath(),outTempDir + "/" + attachmentDO.getName() + uploadPath.substring(uploadPath.lastIndexOf(".")));
                         }else {
                             freemarkerConfig.setEncoding(Locale.getDefault(), "utf-8");
                             freemarkerConfig.setTemplateExceptionHandler(TemplateExceptionHandler.IGNORE_HANDLER);
                             Template template = freemarkerConfig.getTemplate(attachmentDO.getName() + ".xml", "utf-8");
                             response.setHeader("Content-Disposition", "attachment; filename=document." + attachmentDO.getType());
                             response.setCharacterEncoding("utf-8");
                             if ("doc".equals(attachmentDO.getType())) {
                                 response.setContentType("application/msword");
                             } else if ("xls".equals(attachmentDO.getType())) {
                                 response.setContentType("application/msexcel");
                             }
                             File outFile = new File(outTempDir + "/" + attachmentDO.getName() + "." + attachmentDO.getType());
                             //创建输出流
                             FileOutputStream fos = new FileOutputStream(outFile, false);
                             //创建缓冲器
                             Writer out = new BufferedWriter(new OutputStreamWriter(fos, "utf-8"), 10240);
                             template.process(printData, out);
                             out.close();
                             fos.close();
                         }
                     }else if("1".equals(attachmentDO.getCategory())){//统一模版
                         String uploadPath = attachmentDO.getUploadPath();
                         if(StringUtils.isNotEmpty(uploadPath)){
                             HttpUtil.downloadFile(attachmentDO.getUploadPath(),outTempDir + "/" + attachmentDO.getName() + uploadPath.substring(uploadPath.lastIndexOf(".")));
                         }
                     }else if("4".equals(attachmentDO.getCategory())){//人员附件
                         List<MonthlySalaryPersonAttachmentDO> monthlyList = monthlyPersonAttachmentList.stream().filter(attachment -> attachment.getAttachmentName().equals(attachmentDO.getName())).collect(Collectors.toList());
                         if(CollectionUtil.isNotEmpty(monthlyList)){
                             String uploadPath = monthlyList.get(0).getUploadPath();
                             if(StringUtils.isNotEmpty(uploadPath)){
                                 HttpUtil.downloadFile(uploadPath,outTempDir + "/" + monthlyList.get(0).getAttachmentName() + uploadPath.substring(uploadPath.lastIndexOf(".")));
                             }
                         }else{
                             List<PersonAttachmentDO> personList = personAttachmentList.stream().filter(attachment -> attachment.getAttachmentName().equals(attachmentDO.getName())).collect(Collectors.toList());
                             if(CollectionUtil.isNotEmpty(personList)){
                                 String uploadPath = personList.get(0).getUploadPath();
                                 if(StringUtils.isNotEmpty(uploadPath)){
                                     HttpUtil.downloadFile(uploadPath,outTempDir + "/" + personList.get(0).getAttachmentName() + uploadPath.substring(uploadPath.lastIndexOf(".")));
                                 }
                             }
                         }
                     }else if("3".equals(attachmentDO.getCategory()) || "5".equals(attachmentDO.getCategory())){//车辆、保险
                         if(CollectionUtil.isNotEmpty(busAttachmentDOList)){
                             List<BusAttachmentDO> busList = busAttachmentDOList.stream().filter(attachment -> attachment.getAttachmentName().equals(attachmentDO.getName())).collect(Collectors.toList());
                             if(CollectionUtil.isNotEmpty(busList)){
                                 String uploadPath = busList.get(0).getUploadPath();
                                 if(StringUtils.isNotEmpty(uploadPath)){
                                     HttpUtil.downloadFile(uploadPath,outTempDir + "/" + busList.get(0).getAttachmentName() + uploadPath.substring(uploadPath.lastIndexOf(".")));
                                 }
                             }
                         }
                     }
                 }catch(Exception e){
                     throw new RuntimeException(e);
                 }
            });
            File zipFile = ZipUtil.zip(outTempDir);//压缩文件夹
            downloadZip(response,FileUtils.readFileToBytes(zipFile));
            org.apache.commons.io.FileUtils.deleteDirectory(fileDir);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("标牌批量打包下载异常：{}",e.getMessage());
        }
    }

    /**
     * 生成zip文件
     */
    private void downloadZip(HttpServletResponse response, byte[] data) throws IOException
    {
        response.reset();
        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setHeader("Content-Disposition", "attachment; filename=\"sign.zip\"");
        response.addHeader("Content-Length", "" + data.length);
        response.setContentType("application/octet-stream; charset=UTF-8");
        IOUtils.write(data, response.getOutputStream());
    }
}