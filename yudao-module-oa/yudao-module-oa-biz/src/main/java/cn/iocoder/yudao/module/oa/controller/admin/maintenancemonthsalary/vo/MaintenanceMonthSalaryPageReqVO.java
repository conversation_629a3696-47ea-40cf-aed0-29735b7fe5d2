package cn.iocoder.yudao.module.oa.controller.admin.maintenancemonthsalary.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.time.LocalDate;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 维修员工资分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MaintenanceMonthSalaryPageReqVO extends PageParam {

    @Schema(description = "所属公司", example = "王五")
    private String companyName;

    @Schema(description = "部门名称", example = "李四")
    private String departmentName;

    @Schema(description = "部门名称", example = "张三")
    private String deptName;

    @Schema(description = "岗位名称", example = "赵六")
    private String postName;

    @Schema(description = "岗位编码")
    private String postCode;

    @Schema(description = "人员ID", example = "19926")
    private Long personId;

    @Schema(description = "姓名", example = "李四")
    private String employeeName;

    @Schema(description = "个人证件号码")
    private String idNumber;

    @Schema(description = "联系电话")
    private String phone;

    @Schema(description = "银行名称", example = "李四")
    private String bankName;

    @Schema(description = "银行卡号")
    private String bankCard;

    @Schema(description = "入职时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private Date entryDate;

    @Schema(description = "本月出勤天数")
    private BigDecimal attendanceDays;

    @Schema(description = "基本工资")
    private BigDecimal baseSalary;

    @Schema(description = "全勤奖")
    private BigDecimal attendanceBonus;

    @Schema(description = "岗位补贴")
    private BigDecimal postSubsidy;

    @Schema(description = "工龄")
    private BigDecimal seniority;

    @Schema(description = "考核绩效")
    private BigDecimal performance;

    @Schema(description = "月固定加班费")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private BigDecimal fixedOvertime;

    @Schema(description = "用餐补贴")
    private BigDecimal mealSubsidy;

    @Schema(description = "住宿补贴")
    private BigDecimal housingSubsidy;

    @Schema(description = "交通补贴")
    private BigDecimal transportSubsidy;

    @Schema(description = "高温补贴")
    private BigDecimal highTempSubsidy;

    @Schema(description = "摆渡加班费用")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private BigDecimal ferryOvertime;

    @Schema(description = "其他费用")
    private BigDecimal otherExpenses;

    @Schema(description = "正常加班费用")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private BigDecimal normalOvertime;

    @Schema(description = "加班费用总计")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private BigDecimal totalOvertime;

    @Schema(description = "缺勤天数")
    private BigDecimal absenceDays;

    @Schema(description = "缺勤扣款")
    private BigDecimal absenceDeduction;

    @Schema(description = "其他扣款")
    private BigDecimal otherDeduction;

    @Schema(description = "缺勤扣款总计")
    private BigDecimal totalDeduction;

    @Schema(description = "应发工资合计")
    private BigDecimal totalGross;

    @Schema(description = "社保费用")
    private BigDecimal socialInsurance;

    @Schema(description = "公积金费用")
    private BigDecimal housingFund;

    @Schema(description = "社保公积金总计")
    private BigDecimal totalInsurance;

    @Schema(description = "税前工资")
    private BigDecimal preTaxSalary;

    @Schema(description = "个人所得税")
    private BigDecimal incomeTax;

    @Schema(description = "实发工资")
    private BigDecimal netSalary;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;


    @Schema(description = " 薪资所属月份", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern = "yyyy-MM")
    private String occurrenceTime;

}