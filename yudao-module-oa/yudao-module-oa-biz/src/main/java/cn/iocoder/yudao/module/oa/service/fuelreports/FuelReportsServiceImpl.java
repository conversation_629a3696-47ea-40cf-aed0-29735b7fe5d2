package cn.iocoder.yudao.module.oa.service.fuelreports;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.mybatis.core.query.QueryWrapperX;
import cn.iocoder.yudao.module.oa.util.DgTrafficApiUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;
import cn.iocoder.yudao.module.oa.controller.admin.fuelreports.vo.*;
import cn.iocoder.yudao.module.oa.dal.dataobject.fuelreports.FuelReportsDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.oa.dal.mysql.fuelreports.FuelReportsMapper;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.oa.enums.ErrorCodeConstants.*;

/**
 * 油耗日月报 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class FuelReportsServiceImpl implements FuelReportsService {

    @Resource
    private FuelReportsMapper fuelReportsMapper;

    @Resource
    private DgTrafficApiUtil dgTrafficApiUtil;

    @Override
    public Long createFuelReports(FuelReportsSaveReqVO createReqVO) {
        // 插入
        FuelReportsDO fuelReports = BeanUtils.toBean(createReqVO, FuelReportsDO.class);
        fuelReportsMapper.insert(fuelReports);
        // 返回
        return fuelReports.getId();
    }

    @Override
    public void updateFuelReports(FuelReportsSaveReqVO updateReqVO) {
        // 校验存在
        validateFuelReportsExists(updateReqVO.getId());
        // 更新
        FuelReportsDO updateObj = BeanUtils.toBean(updateReqVO, FuelReportsDO.class);
        fuelReportsMapper.updateById(updateObj);
    }

    @Override
    public void deleteFuelReports(Long id) {
        // 校验存在
        validateFuelReportsExists(id);
        // 删除
        fuelReportsMapper.deleteById(id);
    }

    private void validateFuelReportsExists(Long id) {
        if (fuelReportsMapper.selectById(id) == null) {
            throw exception(FUEL_REPORTS_NOT_EXISTS);
        }
    }

    @Override
    public FuelReportsDO getFuelReports(Long id) {
        return fuelReportsMapper.selectById(id);
    }

    @Override
    public PageResult<FuelReportsDO> getFuelReportsPage(FuelReportsPageReqVO pageReqVO) {
        return fuelReportsMapper.selectPage(pageReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String syncFuelReportsData(String startTime, String endTime, String vehiIds, Boolean isMonth) {
        log.info("[syncFuelReportsData][开始同步油耗日月报数据，开始时间：{}，结束时间：{}，车牌号：{}，是否月报：{}]",
                startTime, endTime, StrUtil.isBlank(vehiIds) ? "全部" : vehiIds, isMonth);

        try {
            // 参数校验和默认值设置
            String[] validatedParams = validateAndSetDefaults(startTime, endTime, vehiIds, isMonth);
            String actualStartTime = validatedParams[0];
            String actualEndTime = validatedParams[1];
            String actualVehiIds = validatedParams[2];
            boolean actualIsMonth = Boolean.parseBoolean(validatedParams[3]);

            // 处理车牌号列表
            List<String> vehiIdList = null;
            if (StrUtil.isNotBlank(actualVehiIds)) {
                vehiIdList = StrUtil.split(actualVehiIds, ',', true, true);
            }

            // 分批处理车牌号，每批最多100个
            List<FuelReportsDO> allFuelReports = new ArrayList<>();
            if (CollUtil.isNotEmpty(vehiIdList)) {
                allFuelReports = processBatchVehicles(actualStartTime, actualEndTime, vehiIdList, actualIsMonth);
            } else {
                // 如果没有指定车牌号，直接获取所有数据
                List<FuelReportsDO> fuelReports = dgTrafficApiUtil.getOilDayMonthData(
                        actualStartTime, actualEndTime, null, actualIsMonth, null, null);
                allFuelReports.addAll(fuelReports);
            }
            if (!CollUtil.isEmpty(allFuelReports)) {
                //将fuelReports中的车牌转成 List
                List<String>needDeleteVehicleIds =  allFuelReports.stream().map(FuelReportsDO::getVehildno).collect(Collectors.toList());
                if(isMonth){
                    //需要将actualStartTime的'yyyy-MM'格式转换为'yyyy-MM-dd'格式
                    DateTime dateTime = DateUtil.beginOfMonth(DateUtil.parse(actualStartTime, "yyy-MM-dd"));
                }
                //先查询，再删除
                List<FuelReportsDO> existingFuelReports = fuelReportsMapper.selectList(new QueryWrapperX<FuelReportsDO>()
                        .ge("business_date",actualStartTime+" 00:00:00")
                        .le("business_date",actualEndTime+" 23:59:59")
                        .in("vehildno", needDeleteVehicleIds)
                        .eq("is_month", actualIsMonth));
                if (CollUtil.isNotEmpty(existingFuelReports)) {
                    List<Long> deleteIds = existingFuelReports.stream().map(FuelReportsDO::getId).collect(Collectors.toList());
                    fuelReportsMapper.deleteBatchIds(deleteIds);
                }
                fuelReportsMapper.insertBatch(allFuelReports);
            }

            String resultMessage = StrUtil.format("同步成功，时间范围：{} 至 {}，获取{}条新数据",
                    actualStartTime, actualEndTime, allFuelReports.size());
            log.info("[syncFuelReportsData][{}]", resultMessage);
            return resultMessage;
        } catch (Exception e) {
            log.error("[syncFuelReportsData][同步油耗日月报数据异常]", e);
            return "同步失败：" + e.getMessage();
        }
    }

    /**
     * 参数校验和默认值设置
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param vehiIds   车牌号
     * @param isMonth   是否月报
     * @return 校验后的参数数组 [startTime, endTime, vehiIds, isMonth]
     */
    private String[] validateAndSetDefaults(String startTime, String endTime, String vehiIds, Boolean isMonth) {
        String actualStartTime = StrUtil.blankToDefault(startTime, "");
        String actualEndTime = StrUtil.blankToDefault(endTime, "");
        String actualVehiIds = StrUtil.blankToDefault(vehiIds, "");

        // 1. isMonth为空时，默认按月份
        boolean actualIsMonth = isMonth != null ? isMonth : true;

        // 2. 设置默认时间范围
        if (StrUtil.isBlank(actualStartTime) && StrUtil.isBlank(actualEndTime)) {
            if (actualIsMonth) {
                // 按月份：默认使用上个月的开始和结束时间
                Date lastMonth = DateUtil.offsetMonth(new Date(), -1);
                actualStartTime = DateUtil.format(lastMonth, "yyyy-MM");
                actualEndTime = actualStartTime; // 月报的开始和结束时间相同
                log.info("[validateAndSetDefaults][未指定日期范围，默认使用上个月：{}]", actualStartTime);
            } else {
                // 按日期：默认使用昨天
                String yesterday = DateUtil.formatDate(DateUtil.yesterday());
                actualStartTime = yesterday;
                actualEndTime = yesterday;
                log.info("[validateAndSetDefaults][未指定日期范围，默认使用昨天：{}]", yesterday);
            }
        }

        // 3. 日期格式校验
        validateTimeFormat(actualStartTime, actualIsMonth, "开始时间");
        validateTimeFormat(actualEndTime, actualIsMonth, "结束时间");

        return new String[]{actualStartTime, actualEndTime, actualVehiIds, String.valueOf(actualIsMonth)};
    }

    /**
     * 校验时间格式
     *
     * @param timeStr   时间字符串
     * @param isMonth   是否月报
     * @param fieldName 字段名称（用于错误提示）
     */
    private void validateTimeFormat(String timeStr, boolean isMonth, String fieldName) {
        if (StrUtil.isBlank(timeStr)) {
            return;
        }

        try {
            if (isMonth) {
                // 月份格式校验 yyyy-MM
                if (!timeStr.matches("\\d{4}-\\d{2}")) {
                    throw new IllegalArgumentException("月份格式错误，应为 yyyy-MM 格式");
                }
                // 进一步校验月份的有效性
                DateUtil.parse(timeStr + "-01", "yyyy-MM-dd");
            } else {
                // 日期格式校验 yyyy-MM-dd
                if (!timeStr.matches("\\d{4}-\\d{2}-\\d{2}")) {
                    throw new IllegalArgumentException("日期格式错误，应为 yyyy-MM-dd 格式");
                }
                // 进一步校验日期的有效性
                DateUtil.parseDate(timeStr);
            }
        } catch (Exception e) {
            throw new IllegalArgumentException(fieldName + "格式错误：" + e.getMessage());
        }
    }

    /**
     * 分批处理车辆数据，每批最多100个车牌号
     *
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @param vehiIdList  车牌号列表
     * @param isMonth     是否月报
     * @return 所有批次的数据
     */
    private List<FuelReportsDO> processBatchVehicles(String startTime, String endTime,
                                                    List<String> vehiIdList, boolean isMonth) throws Exception {
        List<FuelReportsDO> allFuelReports = new ArrayList<>();
        int batchSize = 100;
        int totalSize = vehiIdList.size();

        for (int i = 0; i < totalSize; i += batchSize) {
            // 计算当前批次的结束索引
            int endIndex = Math.min(i + batchSize, totalSize);

            // 获取当前批次的车牌号列表
            List<String> batchVehiIds = vehiIdList.subList(i, endIndex);

            log.info("正在获取第 {}/{} 批车辆油耗日月报数据，本批次包含 {} 个车牌",
                     (i / batchSize) + 1,
                     (totalSize + batchSize - 1) / batchSize,
                     batchVehiIds.size());

            // 调用API获取当前批次的数据
            List<FuelReportsDO> batchData = dgTrafficApiUtil.getOilDayMonthData(
                    startTime, endTime, batchVehiIds, isMonth, null, null);

            // 将当前批次的数据添加到总结果中
            if (CollUtil.isNotEmpty(batchData)) {
                allFuelReports.addAll(batchData);
                log.info("成功获取第 {}/{} 批车辆油耗日月报数据，获取到 {} 条记录",
                         (i / batchSize) + 1,
                         (totalSize + batchSize - 1) / batchSize,
                         batchData.size());
            } else {
                log.warn("第 {}/{} 批车辆油耗日月报数据为空",
                         (i / batchSize) + 1,
                         (totalSize + batchSize - 1) / batchSize);
            }

            // 可选：添加一个短暂的延迟，避免频繁请求第三方接口
            try {
                Thread.sleep(500); // 500毫秒延迟
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        return allFuelReports;
    }
}