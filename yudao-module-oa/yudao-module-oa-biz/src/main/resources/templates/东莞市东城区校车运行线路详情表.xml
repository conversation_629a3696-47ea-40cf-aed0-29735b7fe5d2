<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<?mso-application progid="Excel.Sheet"?>
<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet" xmlns:o="urn:schemas-microsoft-com:office:office"
          xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet"
          xmlns:html="http://www.w3.org/TR/REC-html40" xmlns:dt="uuid:C2F41010-65B3-11d1-A29F-00AA00C14882">
    <DocumentProperties xmlns="urn:schemas-microsoft-com:office:office">
        <Author>User</Author>
        <LastAuthor>省</LastAuthor>
        <LastPrinted>2016-12-15T07:37:00Z</LastPrinted>
        <Created>2015-04-15T06:02:00Z</Created>
        <LastSaved>2025-05-21T07:14:49Z</LastSaved>
        <Company>Dongguan</Company>
    </DocumentProperties>
    <CustomDocumentProperties xmlns="urn:schemas-microsoft-com:office:office">
        <KSOProductBuildVer dt:dt="string">2052-12.1.0.20784</KSOProductBuildVer>
        <ICV dt:dt="string">8D422AC7D642420B83265F9A4E76D204_13</ICV>
    </CustomDocumentProperties>
    <ExcelWorkbook xmlns="urn:schemas-microsoft-com:office:excel">
        <WindowWidth>25600</WindowWidth>
        <WindowHeight>12080</WindowHeight>
        <ProtectStructure>False</ProtectStructure>
        <ProtectWindows>False</ProtectWindows>
    </ExcelWorkbook>
    <Styles>
        <Style ss:ID="Default" ss:Name="Normal">
            <Alignment ss:Vertical="Center"/>
            <Borders/>
            <Font ss:FontName="宋体" x:CharSet="134" ss:Size="11" ss:Color="#000000"/>
            <Interior/>
            <NumberFormat/>
            <Protection/>
        </Style>
        <Style ss:ID="s49"/>
        <Style ss:ID="s50">
            <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
        </Style>
        <Style ss:ID="s51">
            <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
            <Font ss:FontName="宋体" x:CharSet="134" ss:Size="25" ss:Color="#000000" ss:Bold="1"/>
        </Style>
        <Style ss:ID="s52">
            <Alignment ss:Horizontal="Left" ss:Vertical="Center"/>
            <Borders/>
            <Font ss:FontName="宋体" x:CharSet="134" ss:Size="12" ss:Color="#000000"/>
        </Style>
        <Style ss:ID="s53">
            <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
            <Borders>
                <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
                <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
                <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
                <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
            </Borders>
            <Font ss:FontName="宋体" x:CharSet="134" ss:Size="12" ss:Color="#000000"/>
        </Style>
        <Style ss:ID="s54">
            <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
            <Borders>
                <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
                <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
                <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
                <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
            </Borders>
            <Font ss:FontName="宋体" x:CharSet="134" ss:Size="12" ss:Color="#000000"/>
            <NumberFormat/>
        </Style>
        <Style ss:ID="s55">
            <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
            <Borders>
                <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
                <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
                <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
                <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
            </Borders>
            <Font ss:FontName="宋体" x:CharSet="134" ss:Size="12" ss:Color="#000000"/>
            <NumberFormat/>
        </Style>
        <Style ss:ID="s56">
            <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
            <Font ss:FontName="宋体" x:CharSet="134" ss:Size="25" ss:Color="#000000" ss:Bold="1"/>
        </Style>
        <Style ss:ID="s57">
            <Alignment ss:Horizontal="Left" ss:Vertical="Center"/>
            <Borders/>
            <Font ss:FontName="宋体" x:CharSet="134" ss:Size="12" ss:Color="#000000"/>
        </Style>
        <Style ss:ID="s58">
            <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
            <Borders>
                <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
                <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
                <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
                <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
            </Borders>
            <Font ss:FontName="宋体" x:CharSet="134" ss:Size="12" ss:Color="#000000"/>
            <NumberFormat/>
        </Style>
        <Style ss:ID="s59">
            <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
            <Borders>
                <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
                <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
                <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
                <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
            </Borders>
            <Font ss:FontName="宋体" x:CharSet="134" ss:Size="12" ss:Color="#000000"/>
            <NumberFormat/>
        </Style>
        <Style ss:ID="s60">
            <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
            <Borders>
                <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
                <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
                <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
                <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
            </Borders>
            <Font ss:FontName="宋体" x:CharSet="134" ss:Size="12" ss:Color="#000000"/>
            <NumberFormat/>
        </Style>
        <Style ss:ID="s61">
            <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
            <Borders>
                <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
                <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
                <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
                <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
            </Borders>
            <Font ss:FontName="宋体" x:CharSet="134" ss:Size="12" ss:Color="#000000"/>
            <NumberFormat/>
        </Style>
        <Style ss:ID="s62">
            <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
            <Borders>
                <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
                <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
                <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
                <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
            </Borders>
            <Font ss:FontName="宋体" x:CharSet="134" ss:Size="12" ss:Color="#000000"/>
            <NumberFormat/>
        </Style>
        <Style ss:ID="s63">
            <Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/>
            <Borders>
                <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
                <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
                <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
                <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
            </Borders>
            <Font ss:FontName="宋体" x:CharSet="134" ss:Size="12" ss:Color="#000000"/>
            <NumberFormat/>
        </Style>
    </Styles>
    <Worksheet ss:Name="Sheet1">
        <Table ss:ExpandedColumnCount="13" ss:ExpandedRowCount="5" x:FullColumns="1" x:FullRows="1"
               ss:DefaultColumnWidth="49.5" ss:DefaultRowHeight="14">
            <Column ss:Index="1" ss:StyleID="s50" ss:AutoFitWidth="0" ss:Width="84.55"/>
            <Column ss:Index="2" ss:StyleID="s50" ss:AutoFitWidth="0" ss:Width="56.4"/>
            <Column ss:StyleID="s50" ss:AutoFitWidth="0" ss:Width="72.9"/>
            <Column ss:StyleID="s50" ss:AutoFitWidth="0" ss:Width="75.65"/>
            <Column ss:StyleID="s50" ss:AutoFitWidth="0" ss:Width="180.15"/>
            <Column ss:StyleID="s50" ss:AutoFitWidth="0" ss:Width="102.45"/>
            <Column ss:StyleID="s50" ss:AutoFitWidth="0" ss:Width="78.4"/>
            <Column ss:StyleID="s50" ss:AutoFitWidth="0" ss:Width="140.95"/>
            <Column ss:StyleID="s50" ss:AutoFitWidth="0" ss:Width="289.45"/>
            <Column ss:StyleID="s50" ss:Span="3"/>
            <Row ss:Height="45">
                <Cell ss:StyleID="s56" ss:MergeAcross="7">
                    <Data ss:Type="String">东莞市东城区校车运行线路详情表</Data>
                </Cell>
            </Row>
            <Row ss:Height="15">
                <Cell ss:StyleID="s57" ss:MergeAcross="7">
                    <Data ss:Type="String">单位（盖章）：${secondParty!}</Data>
                </Cell>
            </Row>
            <Row ss:Height="25.5">
                <Cell ss:StyleID="s53">
                    <Data ss:Type="String">学校名称</Data>
                </Cell>
                <Cell ss:StyleID="s53">
                    <Data ss:Type="String">车牌号码</Data>
                </Cell>
                <Cell ss:StyleID="s53">
                    <Data ss:Type="String">驾驶人</Data>
                </Cell>
                <Cell ss:StyleID="s53">
                    <Data ss:Type="String">联系电话</Data>
                </Cell>
                <Cell ss:StyleID="s53">
                    <Data ss:Type="String">东城区内主要途经线路</Data>
                </Cell>
                <Cell ss:StyleID="s53">
                    <Data ss:Type="String">上下车站点</Data>
                </Cell>
                <Cell ss:StyleID="s53">
                    <Data ss:Type="String">每日趟次</Data>
                </Cell>
                <Cell ss:StyleID="s53">
                    <Data ss:Type="String">运行时间</Data>
                </Cell>
            </Row>
            <Row ss:Height="20">
                <Cell ss:StyleID="s58" ss:MergeDown="${signApplyTimeList?size-1}">
                    <Data ss:Type="String">${firstParty!}</Data>
                </Cell>
                <Cell ss:StyleID="s59" ss:MergeDown="${signApplyTimeList?size-1}">
                    <Data ss:Type="String">${plateNumber!}</Data>
                </Cell>
                <Cell ss:StyleID="s60" ss:MergeDown="${signApplyTimeList?size-1}">
                    <Data ss:Type="String">${driver!}</Data>
                </Cell>
                <Cell ss:StyleID="s61" ss:MergeDown="${signApplyTimeList?size-1}">
                    <Data ss:Type="String">${mobile!}</Data>
                </Cell>
                <Cell ss:StyleID="s53">
                    <Data ss:Type="String">${signApplyTimeList?first.passRoad}</Data>
                </Cell>
                <Cell ss:StyleID="s53">
                    <Data ss:Type="String">${signApplyTimeList?first.dockingStation}</Data>
                </Cell>
                <Cell ss:StyleID="s62" ss:MergeDown="${signApplyTimeList?first.childNumber}">
                    <Data ss:Type="Number">${signApplyTimeList?first.runNumber}</Data>
                </Cell>
                <Cell ss:StyleID="s63" ss:MergeDown="${signApplyTimeList?first.childNumber}">
                    <Data ss:Type="String">${signApplyTimeList?first.departureDate}</Data>
                </Cell>
            </Row>
            <#list signApplyTimeList as signApplyTime>
            <#if signApplyTime_index gt 0>
            <Row ss:Height="20">
                <#if signApplyTime.level == 2>
                <Cell ss:Index="5" ss:StyleID="s53">
                    <Data ss:Type="String">${signApplyTime.passRoad}</Data>
                </Cell>
                <Cell ss:StyleID="s53">
                    <Data ss:Type="String">${signApplyTime.dockingStation}</Data>
                </Cell>
                </#if>
                <#if signApplyTime.level == 1>
                <Cell ss:Index="5" ss:StyleID="s53">
                    <Data ss:Type="String">${signApplyTime.passRoad}</Data>
                </Cell>
                <Cell ss:StyleID="s53">
                    <Data ss:Type="String">${signApplyTime.dockingStation}</Data>
                </Cell>
                <Cell ss:StyleID="s53" ss:MergeDown="${signApplyTime.childNumber}">
                    <Data ss:Type="Number">${signApplyTime.runNumber}</Data>
                </Cell>
                <Cell ss:StyleID="s53" ss:MergeDown="${signApplyTime.childNumber}">
                    <Data ss:Type="String">${signApplyTime.departureDate}</Data>
                </Cell>
                </#if>
            </Row>
            </#if>
            </#list>
        </Table>
        <WorksheetOptions xmlns="urn:schemas-microsoft-com:office:excel">
            <PageSetup>
                <Layout x:Orientation="Landscape"/>
                <Header x:Margin="0.313888888888889"/>
                <Footer x:Margin="0.313888888888889"/>
                <PageMargins x:Left="0.313888888888889" x:Right="0.313888888888889" x:Top="0.0388888888888889"
                             x:Bottom="0.0388888888888889"/>
            </PageSetup>
            <Print>
                <ValidPrinterInfo/>
                <PaperSizeIndex>9</PaperSizeIndex>
                <HorizontalResolution>600</HorizontalResolution>
            </Print>
            <Selected/>
            <TopRowVisible>0</TopRowVisible>
            <LeftColumnVisible>0</LeftColumnVisible>
            <PageBreakZoom>100</PageBreakZoom>
            <Panes>
                <Pane>
                    <Number>3</Number>
                    <ActiveRow>10</ActiveRow>
                    <ActiveCol>4</ActiveCol>
                    <RangeSelection>R11C5</RangeSelection>
                </Pane>
            </Panes>
            <ProtectObjects>False</ProtectObjects>
            <ProtectScenarios>False</ProtectScenarios>
        </WorksheetOptions>
    </Worksheet>
</Workbook>