<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.oa.dal.mysql.pathways.PathWaysMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectNameById" resultType="java.lang.String">
        SELECT way_name FROM t_path_ways WHERE id = #{point} and deleted = 0
    </select>
    <select id="selectWayPoint" resultType="cn.iocoder.yudao.module.oa.dal.dataobject.pathways.PathWaysDO">
            SELECT * FROM t_path_ways WHERE way_name=#{wayName} and deleted = 0
    </select>
    <select id="selectIdByLaLo" resultType="java.lang.Integer">
        SELECT id FROM t_path_ways WHERE way_name=#{wayName} and deleted = 0
    </select>
    <select id="selectByLocation" resultType="cn.iocoder.yudao.module.oa.dal.dataobject.pathways.PathWaysDO">
        SELECT * FROM t_path_ways WHERE start_location=#{startLocation} and end_location=#{endLocation} and deleted = 0
    </select>


</mapper>
